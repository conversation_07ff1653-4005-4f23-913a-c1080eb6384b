<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ProfileUpdateNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $user;

    public $info;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $info)
    {
        $this->user = $user;
        $this->info = $info;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $user = $this->user;
        $info = $this->info;

        return $this->subject('Your Profile is Updated')->view('emails.profile-update-notification', compact('user', 'info'));
    }
}
