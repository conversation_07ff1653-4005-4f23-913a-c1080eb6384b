<?php

namespace App\Console\Commands;

use App\Models\Site;
use App\Services\LightsailService;
use App\Services\Route53DomainsService;
use App\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class InstallSite extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'toprankon:installsite';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('test cmd');
        Site::where('status', 'queued')->get()->each(function (Site $site) {
            try {
                Log::info('test cmd2 '.$site->static_ip_name);
                $user = User::find($site->user_id);
                if ($user->aws_account_id != null) {
                    if (! (new LightsailService($user->aws_account_id, $site->aws_region))->getKeyPair($site->private_key_name)) {

                        if ($site->theme_type == 'demo') {
                            $private_key_name = 'PrivateKey_'.$site->id;
                            $KeyPair = (new LightsailService($user->aws_account_id, $site->aws_region))->createKeyPair($private_key_name);
                            $private_key = $KeyPair['privateKeyBase64'] ? $KeyPair['privateKeyBase64'] : '';
                        } else {
                            $parent = Site::find($site->backup_site_id);
                            $private_key_name = $parent->private_key_name;
                            $private_key = $parent->private_key;
                        }

                        $site->update([
                            'private_key_name' => $private_key_name,
                            'private_key' => $private_key,
                        ]);
                    }
    
                    if (! (new LightsailService($user->aws_account_id, $site->aws_region))->getInstance($site->instance_name)) {
                        if ($site->theme_type == 'demo') {
                            (new LightsailService($user->aws_account_id, $site->aws_region))->createInstance($site);
                        } else {
                            (new LightsailService($user->aws_account_id, $site->aws_region))->createInstancesFromSnapshot($site);
                        }
                    }
    
                    if ($site->domain && $site->domain_type != 'free_domain' && ! (new LightsailService($user->aws_account_id, $site->aws_region))->domainDetails($site->domain)['dns']) {
                        (new LightsailService($user->aws_account_id, $site->aws_region))->createDomain($site->domain);
                    }
    
                    if ($site->static_ip_name) {
                        $ip = (new LightsailService($user->aws_account_id, $site->aws_region))->getStaticIp($site->static_ip_name);
                        if ($ip['status'] == 404) {
                            (new LightsailService($user->aws_account_id, $site->aws_region))->createStaticIp($site->static_ip_name);
                            sleep(3);
                            $staticIp = (new LightsailService($user->aws_account_id, $site->aws_region))->getStaticIp($site->static_ip_name);
                            if ($site->domain && $site->domain_type != 'free_domain') {
                                (new LightsailService($user->aws_account_id, $site->aws_region))->addDomainResource($site->domain, $staticIp['staticIp']['ipAddress']);
                            }
                        }
                    }
    
                    $staticIp = (new LightsailService($user->aws_account_id, $site->aws_region))->getStaticIp($site->static_ip_name);
                    $instance = (new LightsailService($user->aws_account_id, $site->aws_region))->getInstance($site->instance_name);
    
                    if ($instance['instance']['state']['name'] == 'running' && $staticIp && ! $staticIp['staticIp']['isAttached']) {
                        (new LightsailService($user->aws_account_id, $site->aws_region))->attachStaticIp($site->instance_name, $site->static_ip_name);
                    }
    
                    if (isset($instance)) {
                        Log::info('test cmd5 '.$site->static_ip_name);
                        $createdAt = date('Y-m-d H:i:s', strtotime($site->updated_at.' +4 minutes'));
                        $new = date('Y-m-d H:i:s', strtotime(now()));
                        if ($instance['instance']['state']['name'] == 'running' && $createdAt < $new) {
    
                            Log::info('test cmd6 '.$site->static_ip_name);
                            //this code right here To reduce google domain request
                            if ($site->domain && $site->domain_type != 'free_domain') {
                                $dns_zones = (new LightsailService($user->aws_account_id, $site->aws_region))->domainDetails($site->domain)['dns'];
                                if ($site->domain_type == 'purchase_domain') {
    
                                    Log::info('test cmd7 '.$site->static_ip_name);
                                    if ((new Route53DomainsService)->checkAvailability($site->domain)['Availability'] == 'AVAILABLE') {
                                        Log::info('test cmd3 ');
                                        $site->update(['dns_list' => json_encode($dns_zones)]);
                                        Log::info('test cmd4 ');
                                        (new Route53DomainsService)->registrationDomain($site);
                                    }
                                }
                            }
    
                            Log::info('Enter Into Command & Install site id : '.$site->id);
    
                            $site->update(['status' => 'building']);
                            try {
                                \App\Jobs\InstallSite::dispatch($site, $instance['instance']['publicIpAddress'])->delay(now());
                            } catch (\Exception $exception) {
    
                                // for latest updated at
                                Log::info(['cmd Install Site: demo installing error', $exception]);
                                $site->update(['status' => 'queued']);
                            }
    
                            try {
                                (new LightsailService($site->user->aws_account_id, $site->aws_region))->addFirewallRule($site->instance_name);
                            } catch (\Exception $exception) {
                                // for latest updated at
                                Log::info('cmd Install Site: addFirewallRule');
                                $site->update(['status' => 'queued']);
                            }
                        }
                    }
                }
            } catch (\Exception $exception) {
                $site->update(['status' => 'queued']);
                Log::error('site id '.$site->id.$exception->getMessage());
            }
        });
    }
}
