<?php

namespace App\Http\Controllers;

use App\Mail\TransactionMade;
use App\Models\PaymentPlatform;
use App\Models\ProfessionalServicePlan;
use App\Models\ServiceCategories;
use App\Models\Subscription;
use App\Resolver\PaymentPlatformResolver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Jenssegers\Agent\Agent;

class ProfessionalServiceController extends Controller
{
    /**
     * @var ;
     */
    protected $paymentPlatformResolver;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(PaymentPlatformResolver $paymentPlatformResolver)
    {
        $this->paymentPlatformResolver = $paymentPlatformResolver;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $subscriptions = Subscription::where('serviceable_type', \App\Models\ProfessionalService::class)->get();
        $data = [
            'is_phone' => (new Agent)->isPhone() ? 1 : 0,
            'professionalServiceCategories' => ServiceCategories::all(),
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'payment_methods' => auth()->user()->payment_methods,
            'subscriptions' => $subscriptions,
            'is_windows' => (new Agent)->is('Windows'),
        ];

        return view('professional_service.index', $data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = [];
        // Mail::to(User::find($site->user_id)->email)->send(new TransactionMade($site));
        //Mail::to('<EMAIL>')->send(new TransactionMade($data));
        session()->forget(codibu_session());

        $plan = ProfessionalServicePlan::find(request('plan'));
        $arr = array_merge($request->all(), [
            'paypal_service_plan_id' => $plan->paypal_plan_id,
            'stripe_service_plan_id' => $plan->stripe_plan_id,
            'service_category_name' => $plan->category->name,
            'service_plan' => $plan->id,
        ]);
        $data = priceCalculation([
            'type' => 'professional_service',
            'coupon_code' => request('coupon_code'),
            'plan_id' => request('plan'),
        ]);
        $arr = array_merge($arr, $data);

        session()->put($arr);

        // payment start
        if ($request->payment_platform) {
            $paymentPlatform = $this->paymentPlatformResolver->resolveServices(session('payment_platform'));
            $paypal_url = '';
            if ($plan->duration != 'one-time') {
                $paypal_url = $paymentPlatform->handleProfessionalServiceSubscription();
            } else {
                $paypal_url = $paymentPlatform->createOneTimePayment();
            }

        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }
        //payment end

        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => $request->payment_platform,
        ];

        return json_encode($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id) {
        $subscription = Subscription::find($id);
        //dd($subscription->subscribable->service_category_id);
        //dd($subscriptions->subscribable->id);
        $data = [
            'is_phone' => (new Agent)->isPhone() ? 1 : 0,
            'professionalServiceCategory' => ServiceCategories::find($subscription->subscribable->service_category_id),
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'payment_methods' => auth()->user()->payment_methods,
            'subscription' => $subscription,
            'is_windows' => (new Agent)->is('Windows'),
        ];

        return view('professional_service.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = [];
        session()->forget(codibu_session());

        $oldSubscription = Subscription::find($id);
        $plan = ProfessionalServicePlan::find(request('plan'));
        $arr = array_merge($request->all(), [
            'paypal_service_plan_id' => $plan->paypal_plan_id,
            'stripe_service_plan_id' => $plan->stripe_plan_id,
            'service_category_name' => $plan->category->name,
            'service_plan' => $plan->id,
            'Pre_Subscription_id' => $id,
            'next_billing_date' => $oldSubscription->next_billing_date
        ]);
        $data = priceCalculation([
            'type' => 'professional_service',
            'coupon_code' => request('coupon_code'),
            'plan_id' => request('plan'),
        ]);
        $arr = array_merge($arr, $data);

        session()->put($arr);

        // payment start
        if ($request->payment_platform) {
            $paymentPlatform = $this->paymentPlatformResolver->resolveServices(session('payment_platform'));
            $paypal_url = '';
            if ($plan->duration != 'one-time') {
                $paypal_url = $paymentPlatform->handleProfessionalServiceSubscription();
            } else {
                $paypal_url = $paymentPlatform->createOneTimePayment();
            }

        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }
        //payment end

        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => $request->payment_platform,
        ];

        return json_encode($data);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
