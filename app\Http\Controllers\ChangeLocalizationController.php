<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ChangeLocalizationController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke($locale)
    {
        session()->put('locale', $locale);

        return session('locale');
    }
}
