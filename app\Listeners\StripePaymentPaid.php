<?php

namespace App\Listeners;

use App\Models\Subscription;
use App\Services\WebhookService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookClient\Models\WebhookCall;

class StripePaymentPaid implements ShouldQueue
{
    public function handle(WebhookCall $webhookCall)
    {
        // do your work here
        $object = $webhookCall->payload['data']['object'];
        $subscriptionId = $object['subscription'];
        $subscription = Subscription::where('subscription_id', $subscriptionId)->first();
        Log::info(json_encode($object, true));
        (new WebhookService)->completePaymentMail($subscription);
    }
}
