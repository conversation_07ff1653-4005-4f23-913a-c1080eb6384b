<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class CheckSwap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'swap:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the current swap size';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // return 0;
        $process = new Process(['free', '-h']);
        $process->run();

        if ($process->isSuccessful()) {
            $output = $process->getOutput();
            // Filter the swap line from the free command's output
            $swapLine = array_values(array_filter(explode("\n", $output), function ($line) {
                return stripos($line, 'Swap:') !== false;
            }))[0] ?? '';

            $this->info("Swap Information: $swapLine");
        } else {
            $this->error('Failed to retrieve swap size.');
        }
    }
}
