<?php

namespace App\Admin\Controllers;

use App\Models\ProfessionalServicePlan;
use App\Models\ServiceCategories;
use App\Services\PaypalService;
use App\Services\StripeService;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class ProfessionalServicePlanController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Professional Service Plan';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ProfessionalServicePlan);

        $grid->model()->whereNull('custom_invoice');
        $grid->column('id', __('Id'));
        $grid->column('name', __('Name'));
        $grid->column('category.name', __('Category'));
        $grid->column('price', __('Price'));
        $grid->column('duration', __('Duration'));
        $grid->column('description', __('Description'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {

        $show = new Show(ProfessionalServicePlan::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('Name'));
        $show->field('category.name', __('Category'));
        $show->field('price', __('Price'));
        $show->field('duration', __('Duration'));
        $show->field('description', __('Description'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ProfessionalServicePlan);
        $duration = [
            'one-time' => 'One Time',
            'week' => 'Week',
            'month' => 'Month',
            'year' => 'Year',
        ];
        $categories = ServiceCategories::all()->pluck('name', 'id');
        $form->text('name', __('Name'))->required();
        $form->select('service_category_id', __('Category'))->options($categories)->required();
        /*if (substr(trim(request()->path(), '/'), -5) == '/edit') {
            $form->select('duration', __('Duration'))->options($duration);
            $form->hidden('duration');
            $form->number('price', __('Price'));
            $form->hidden('price');
        } else {*/
        $form->select('duration', __('Duration'))->options($duration)->required();
        $form->number('price', __('Price'))->required();
        /*}*/
        $form->textarea('description', __('Description'));
        $form->hidden('paypal_plan_id');
        $form->hidden('stripe_plan_id');

        $form->saved(function (Form $form) {
            if ($form->duration != 'one-time') {
                /*if ($form->model()->toArray()["paypal_plan_id"] ==null) {
                    (new PaypalService())->createProfessionServicePlan($form->model()->toArray()["id"]);
                }
                if ($form->model()->toArray()["stripe_plan_id"] ==null) {
                    (new StripeService())->createProfessionServicePlan($form->model()->toArray()["id"]);
                }*/
                (new PaypalService)->createProfessionServicePlan($form->model()->toArray()['id']);
                (new StripeService)->createProfessionServicePlan($form->model()->toArray()['id']);
            }
        });

        return $form;
    }
}
