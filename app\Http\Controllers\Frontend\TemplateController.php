<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Demo;
use App\Models\SiteType;
use App\Models\Style;
use App\Services\ThemeFilter;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON>\Agent\Agent;

class TemplateController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        $data = [
            'is_phone' => (new Agent)->isPhone(),
            'site_types' => SiteType::pluck('name', 'name'),
            'styles' => Style::all(),
            'categories' => Category::orderBy('order')->get(),
        ];

        if (! $request->page) {
            $randomIds = Demo::query()->pluck('id')->shuffle()->toArray();
            session(['random_ids' => $randomIds]);
        }
        //theme filtering from services class
        $data['themes'] = (new ThemeFilter)->filteringThemes($request);

        return view('frontend.pages.templates', $data);
    }
}
