<?php

use Carbon\Carbon;

/**
 * return formatted date
 */
if (! function_exists('formatted_date')) {
    function formatted_date($date): string
    {
        return Carbon::parse($date)->format('F d, Y');
    }
}
if (! function_exists('formatted_date_short')) {
    function formatted_date_short($date): string
    {
        return Carbon::parse($date)->format('M').' '.Carbon::parse($date)->format('d').'<sup>'.Carbon::parse($date)->format('S').'</sup>'.' '.Carbon::parse($date)->format('Y');
    }
}

if (! function_exists('lan_check')) {
    function lan_check($currentUrl): string
    {
        if (session('locale') == 'en') {
            return $currentUrl.'/kr';
        } else {
            return str_replace('/kr', '', $currentUrl);
        }
    }
}

if(! function_exists('aws_regions')){
    function aws_regions(){
        return  [
            'us-east-1a' => 'United States, N. Virginia(Zone A)',
            'us-east-1b' => 'United States, N. Virginia(Zone B)',
            'us-east-1c' => 'United States, N. Virginia(Zone C)',
            'us-east-1d' => 'United States, N. Virginia(Zone D)',
            'us-east-1e' => 'United States, N. Virginia(Zone E)',
            'us-east-1f' => 'United States, N. Virginia(Zone F)',
            'us-east-2a' => 'United States, Ohio(Zone A)',
            'us-east-2b' => 'United States, Ohio(Zone B)',
            'us-east-2c' => 'United States, Ohio(Zone C)',
            //'us-west-1' => 'United States, N. California()',
            'ca-central-1a' => 'Canada, Central(Zone A)',
            'ca-central-1b' => 'Canada, Central(Zone B)',
            'ca-central-1d' => 'Canada, Central(Zone D)',
            'us-west-2a' => 'United States, Oregon(Zone A)',
            'us-west-2b' => 'United States, Oregon(Zone B)',
            'us-west-2c' => 'United States, Oregon(Zone C)',
            'us-west-2d' => 'United States, Oregon(Zone D)',
            'eu-west-1a' => 'Europe, Ireland(Zone A)',
            'eu-west-1b' => 'Europe, Ireland(Zone B)',
            'eu-west-1c' => 'Europe, Ireland(Zone C)',
            'eu-west-2a' => 'Europe, London(Zone A)',
            'eu-west-2b' => 'Europe, London(Zone B)',
            'eu-west-2c' => 'Europe, London(Zone C)',
            'eu-west-3a' => 'Europe, Paris(Zone A)',
            'eu-west-3b' => 'Europe, Paris(Zone B)',
            'eu-west-3c' => 'Europe, Paris(Zone C)',
            'eu-central-1a' => 'Europe, Frankfurt(Zone A)',
            'eu-central-1b' => 'Europe, Frankfurt(Zone B)',
            'eu-central-1c' => 'Europe, Frankfurt(Zone C)',
            'eu-north-1a' => 'Europe, Stockholm(Zone A)',
            'eu-north-1b' => 'Europe, Stockholm(Zone B)',
            'eu-north-1c' => 'Europe, Stockholm(Zone C)',
            'ap-northeast-1a' => 'Asia Pacific, Tokyo(Zone A)',
            'ap-northeast-1c' => 'Asia Pacific, Tokyo(Zone C)',
            'ap-northeast-1d' => 'Asia Pacific, Tokyo(Zone D)',
            'ap-southeast-2a' => 'Asia Pacific, Sydney(Zone A)',
            'ap-southeast-2b' => 'Asia Pacific, Sydney(Zone B)',
            'ap-southeast-2c' => 'Asia Pacific, Sydney(Zone C)',
            'ap-south-1a' => 'Asia Pacific, Mumbai(Zone A)',
            'ap-south-1b' => 'Asia Pacific, Mumbai(Zone B)',
            'ap-south-1c' => 'Asia Pacific, Mumbai(Zone C)',
            'ap-northeast-2a' => 'Asia Pacific, Seoul(Zone A)',
            'ap-northeast-2b' => 'Asia Pacific, Seoul(Zone B)',
            'ap-northeast-2c' => 'Asia Pacific, Seoul(Zone C)',
            'ap-northeast-2d' => 'Asia Pacific, Seoul(Zone D)',
            'ap-southeast-1a' => 'Asia Pacific, Singapore(Zone A)',
            'ap-southeast-1b' => 'Asia Pacific, Singapore(Zone B)',
            'ap-southeast-1c' => 'Asia Pacific, Singapore(Zone C)',
            //'ap-northeast-3' => 'Asia Pacific, Osaka(Zone A)',
            //'ap-southeast-7' => 'Asia Pacific, Thailand(Zone A)',
            //'ca-central-1' => 'Canada, Central(Zone A)',
            //'eu-central-1' => 'Europe, Frankfurt(Zone A)',
            //'sa-east-1' => 'South America, São Paulo(Zone A)'
        ];

    }
}

if (! function_exists('google_domain_prices')) {
    function google_domain_prices(): array
    {
        return [
            ['.academy', '30'],
            ['.accountant', '30'],
            ['.actor', '40'],
            ['.agency', '20'],
            ['.airforce', '30'],
            ['.apartments', '60'],
            ['.app', '14'],
            ['.army', '30'],
            ['.art', '14'],
            ['.associates', '30'],
            ['.attorney', '40'],
            ['.auction', '30'],
            ['.autos', '15'],
            ['.band', '20'],
            ['.bargains', '30'],
            ['.best', '20'],
            ['.bid', '30'],
            ['.bike', '30'],
            ['.bingo', '50'],
            ['.biz', '15'],
            ['.black', '60'],
            ['.blog', '30'],
            ['.blue', '20'],
            ['.boats', '15'],
            ['.boutique', '30'],
            ['.builders', '30'],
            ['.business', '12'],
            ['.buzz', '40'],
            ['.cab', '30'],
            ['.cafe', '40'],
            ['.camera', '40'],
            ['.camp', '40'],
            ['.capital', '50'],
            ['.cards', '30'],
            ['.care', '30'],
            ['.careers', '50'],
            ['.cash', '30'],
            ['.catering', '30'],
            ['.cc', '20'],
            ['.center', '20'],
            ['.charity', '30'],
            ['.chat', '40'],
            ['.cheap', '30'],
            ['.church', '40'],
            ['.city', '20'],
            ['.claims', '60'],
            ['.cleaning', '40'],
            ['.clinic', '50'],
            ['.clothing', '30'],
            ['.cloud', '20'],
            ['.club', '13'],
            ['.co', '30'],
            ['.co.in', '11'],
            ['.co.nz', '19'],
            ['.co.uk', '12'],
            ['.coach', '60'],
            ['.codes', '50'],
            ['.coffee', '30'],
            ['.com', '12'],
            ['.com.mx', '20'],
            ['.community', '30'],
            ['.company', '14'],
            ['.computer', '30'],
            ['.contact', '12'],
            ['.condos', '50'],
            ['.construction', '30'],
            ['.consulting', '30'],
            ['.contractors', '30'],
            ['.cool', '30'],
            ['.coupons', '60'],
            ['.cricket', '30'],
            ['.cruises', '50'],
            ['.dance', '20'],
            ['.date', '30'],
            ['.dating', '50'],
            ['.day', '12'],
            ['.de', '7'],
            ['.deals', '40'],
            ['.degree', '40'],
            ['.delivery', '60'],
            ['.democrat', '30'],
            ['.dental', '50'],
            ['.dentist', '40'],
            ['.design', '40'],
            ['.dev', '12'],
            ['.diamonds', '50'],
            ['.digital', '40'],
            ['.direct', '40'],
            ['.directory', '20'],
            ['.discount', '30'],
            ['.dog', '40'],
            ['.domains', '30'],
            ['.download', '30'],
            ['.earth', '20'],
            ['.education', '20'],
            ['.email', '20'],
            ['.engineer', '30'],
            ['.engineering', '50'],
            ['.enterprises', '30'],
            ['.equipment', '20'],
            ['.estate', '30'],
            ['.events', '30'],
            ['.exchange', '30'],
            ['.expert', '50'],
            ['.exposed', '20'],
            ['.express', '30'],
            ['.fail', '30'],
            ['.faith', '30'],
            ['.family', '20'],
            ['.fan', '40'],
            ['.fans', '12'],
            ['.farm', '30'],
            ['.finance', '60'],
            ['.financial', '50'],
            ['.fish', '30'],
            ['.fitness', '30'],
            ['.flights', '50'],
            ['.florist', '30'],
            ['.football', '30'],
            ['.forsale', '30'],
            ['.foundation', '30'],
            ['.fr', '10'],
            ['.fun', '20'],
            ['.fund', '50'],
            ['.furniture', '50'],
            ['.futbol', '13'],
            ['.fyi', '20'],
            ['.gallery', '20'],
            ['.games', '20'],
            ['.gifts', '40'],
            ['.gives', '30'],
            ['.glass', '40'],
            ['.gmbh', '30'],
            ['.golf', '60'],
            ['.graphics', '20'],
            ['.gratis', '20'],
            ['.gripe', '30'],
            ['.group', '20'],
            ['.guide', '40'],
            ['.guru', '28'],
            ['.haus', '28'],
            ['.healthcare', '60'],
            ['.hockey', '60'],
            ['.holdings', '50'],
            ['.holiday', '50'],
            ['.homes', '15'],
            ['.hospital', '50'],
            ['.house', '30'],
            ['.how', '30'],
            ['.icu', '12'],
            ['.immo', '40'],
            ['.immobilien', '30'],
            ['.in', '12'],
            ['.industries', '30'],
            ['.info', '12'],
            ['.ink', '28'],
            ['.institute', '20'],
            ['.insure', '60'],
            ['.international', '20'],
            ['.io', '60'],
            ['.irish', '14'],
            ['.jetzt', '20'],
            ['.jewelry', '60'],
            ['.jp', '40'],
            ['.kaufen', '30'],
            ['.kitchen', '40'],
            ['.land', '30'],
            ['.lawyer', '40'],
            ['.lease', '50'],
            ['.legal', '60'],
            ['.life', '40'],
            ['.lighting', '20'],
            ['.limited', '30'],
            ['.limo', '50'],
            ['.live', '20'],
            ['.llc', '30'],
            ['.loan', '30'],
            ['.love', '30'],
            ['.ltd', '20'],
            ['.luxury', '40'],
            ['.maison', '50'],
            ['.management', '20'],
            ['.market', '40'],
            ['.marketing', '30'],
            ['.mba', '40'],
            ['.me', '20'],
            ['.media', '30'],
            ['.memorial', '60'],
            ['.men', '30'],
            ['.mobi', '20'],
            ['.moda', '30'],
            ['.money', '40'],
            ['.mortgage', '40'],
            ['.mx', '40'],
            ['.navy', '30'],
            ['.net', '12'],
            ['.network', '20'],
            ['.news', '30'],
            ['.ninja', '19'],
            ['.nl', '8'],
            ['.one', '12'],
            ['.online', '30'],
            ['.ooo', '30'],
            ['.org', '12'],
            ['.page', '10'],
            ['.partners', '50'],
            ['.parts', '30'],
            ['.party', '30'],
            ['.photography', '20'],
            ['.photos', '20'],
            ['.pictures', '11'],
            ['.pizza', '60'],
            ['.place', '15'],
            ['.plumbing', '40'],
            ['.plus', '40'],
            ['.press', '60'],
            ['.pro', '20'],
            ['.productions', '30'],
            ['.promo', '20'],
            ['.properties', '30'],
            ['.pub', '30'],
            ['.pw', '9'],
            ['.racing', '30'],
            ['.recipes', '50'],
            ['.red', '20'],
            ['.rehab', '30'],
            ['.reisen', '20'],
            ['.rentals', '30'],
            ['.repair', '30'],
            ['.report', '20'],
            ['.republican', '30'],
            ['.rest', '40'],
            ['.restaurant', '60'],
            ['.review', '30'],
            ['.reviews', '20'],
            ['.rip', '20'],
            ['.rocks', '13'],
            ['.run', '20'],
            ['.sale', '30'],
            ['.salon', '50'],
            ['.sarl', '40'],
            ['.school', '40'],
            ['.schule', '20'],
            ['.science', '30'],
            ['.services', '30'],
            ['.shoes', '40'],
            ['.shopping', '30'],
            ['.show', '40'],
            ['.singles', '30'],
            ['.site', '20'],
            ['.ski', '50'],
            ['.soccer', '20'],
            ['.social', '30'],
            ['.software', '40'],
            ['.solar', '40'],
            ['.solutions', '20'],
            ['.soy', '20'],
            ['.space', '20'],
            ['.store', '50'],
            ['.stream', '30'],
            ['.studio', '20'],
            ['.style', '40'],
            ['.supplies', '20'],
            ['.supply', '20'],
            ['.support', '20'],
            ['.surgery', '50'],
            ['.systems', '20'],
            ['.tax', '50'],
            ['.taxi', '60'],
            ['.team', '40'],
            ['.tech', '40'],
            ['.technology', '20'],
            ['.tennis', '60'],
            ['.theater', '60'],
            ['.tienda', '50'],
            ['.tips', '20'],
            ['.today', '20'],
            ['.tools', '30'],
            ['.tours', '60'],
            ['.town', '30'],
            ['.toys', '40'],
            ['.trade', '30'],
            ['.training', '30'],
            ['.tube', '30'],
            ['.uk', '12'],
            ['.university', '50'],
            ['.uno', '20'],
            ['.vacations', '30'],
            ['.vegas', '60'],
            ['.ventures', '50'],
            ['.vet', '30'],
            ['.viajes', '50'],
            ['.video', '20'],
            ['.villas', '50'],
            ['.vin', '60'],
            ['.vision', '30'],
            ['.voyage', '50'],
            ['.watch', '30'],
            ['.webcam', '30'],
            ['.website', '20'],
            ['.wiki', '28'],
            ['.win', '30'],
            ['.wine', '60'],
            ['.works', '30'],
            ['.world', '40'],
            ['.wtf', '30'],
            ['.xyz', '12'],
            ['.zone', '30'],
        ];
    }
}
if (! function_exists('codibu_session')) {
    function codibu_session(): array
    {
        return [
            'aws_account_id',
            'site_create_limit',
            'theme_id',
            'demo_id',
            'theme_type',
            'search',
            'title',
            'password',
            'domain',
            'domain_type',
            'domain_price',
            'plan',
            'session_domain',
            'session_domain_price',
            'session_plan_name',
            'session_plan_price',
            'session_plan_discount',
            'coupon_code',
            'session_coupon_discount',
            'session_total_price',
            'first_name',
            'last_name',
            'phone',
            'company_name',
            'street_address',
            'country',
            'city',
            'state',
            'zip',
            'payment_platform',
            'payment_method',
            'status',
            'msg',
            'plan_name',
            'plan_price',
            'plan_duration',
            'plan_duration_count',
            'total_price',
            'coupon_discount',
            'coupon_id',
            'coupon_code',
            'order_no',
            'subscription_plan_id',
            'subscription_id',
            'domain_subscription_id',
            'service_subscription_id',
            'route_id',
            'renew_auto',
            'name',
            'site_id',
            'start_from',
            'order_id',
            'capture_id',
            'charge_id',
            'automatic_backup_subscription_id',
            'backupDate',
            'backupSiteId',
            'change_billing_id',
            'billing_start_from',
            'billing_subscription_id',
            'restore_deleted_site',
            'price',
            'transfer_domain_price',
            'transfer_domain_subscription_id',
            'service_plan',
            'Pre_Subscription_id',
            'next_billing_date',
            'service_plan_duration',
            'stripe_service_plan_id',
            'paypal_service_plan_id',
            'service_category_name',
            'service_plan_for_cat_1',
            'service_plan_price_for_cat_1',
            'service_plan_duration_for_cat_1',
            'service_plan_for_cat_2',
            'service_plan_price_for_cat_2',
            'service_plan_duration_for_cat_2',
            'service_plan_for_cat_3',
            'service_plan_price_for_cat_3',
            'service_plan_duration_for_cat_3',
            'service_plan_for_cat_4',
            'service_plan_price_for_cat_4',
            'service_plan_duration_for_cat_4',
            'aws_region',
        ];
    }
}
if (! function_exists('reserved_subdomain')) {
    function reserved_subdomain(): array
    {
        return [
            'be',
            'bk',
            'cpanel',
            'damrc',
            'dmarc',
            'demo1',
            'help',
            'live',
            'mail',
            'p',
            'premium',
            'restaurant',
            'shop',
            'test',
            'webmail',
            'whm',
            'www',
            'codibu',
        ];
    }
}
