<?php

namespace App\Mail;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SubscriptionPaymentComplete extends Mailable
{
    use Queueable, SerializesModels;

    public $subscription;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Subscription $subscription)
    {
        if ($subscription->platform == 'Stripe') {
            $subscription->platform = 'Credit card';
        } elseif ($subscription->platform == 'PayPal') {
            $subscription->platform = 'Paypal';
        }
        $this->subscription = $subscription;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'CODIBU')
            ->subject('Your Subscription Payment completed for '.$this->subscription->serviceable_name)
            ->view('emails.payment_complete');
    }
}
