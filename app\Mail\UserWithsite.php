<?php

namespace App\Mail;

use App\Models\Site;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserWithsite extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The site instance.
     *
     * @var Site
     */
    public $site;

    public $is_new = false;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Site $site, $is_new = false)
    {
        $this->site = $site;
        $this->is_new = $is_new;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'CODIBU')
            ->subject('Your website is being created!')
            ->view('emails.sites.userwithsite');
    }
}
