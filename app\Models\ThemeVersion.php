<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class ThemeVersion extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'created_at',
    ];

    //
    public function theme()
    {
        return $this->belongsTo(Theme::class);
    }

    public function s3_url()
    {
        $path = "themes/{$this->theme->slug}__{$this->version}.zip";

        return Storage::disk('s3')->temporaryUrl($path, Carbon::now()->addMinutes(50));
    }
}
