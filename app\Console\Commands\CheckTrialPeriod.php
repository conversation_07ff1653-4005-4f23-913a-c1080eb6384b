<?php

namespace App\Console\Commands;

use App\Mail\TrialInstanceMail;
use App\Models\Site;
use App\Services\LightsailService;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class CheckTrialPeriod extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'toprankon:CheckTrialPeriod';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Site::where('status', '!=', 'stopped')
            ->doesnthave('subscription')
            ->get()->each(function (Site $site) {
                if (Carbon::parse($site->created_at)->addWeek() < Carbon::today()) {
                    (new LightsailService(User::find($site->user_id)->aws_account_id, $site->aws_region))->stopInstance($site);
                    $site->status = 'stopped';
                    $site->save();
                    Mail::to(User::find($site->user_id)->email)->send(new TrialInstanceMail($site));

                    return redirect()->back()->with('success', 'Website Stoped request has been sent.');
                } else {
                    return redirect()->back()->with('success', 'Website Stoped request Faild!.');
                }
            });

        Site::where('status', 'stopped')
            ->doesnthave('subscription')
            ->get()->each(function (Site $site) {
                if (Carbon::parse($site->created_at)->addWeeks(2) < Carbon::today()) {
                    $site->delete();

                    return redirect()->back()->with('success', 'Website Stoped request has been sent.');
                } else {
                    return redirect()->back()->with('success', 'Website Stoped request Faild!.');
                }
            });

    }
}
