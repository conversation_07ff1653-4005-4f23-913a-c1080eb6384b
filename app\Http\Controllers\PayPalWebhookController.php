<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Services\WebhookService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use PayPal\Api\WebhookEvent;

class PayPalWebhookController extends Controller
{
    public function handleWebhook(Request $request)
    {
        $apiContext = new \PayPal\Rest\ApiContext(
            new \PayPal\Auth\OAuthTokenCredential(
                config('services.paypal.client_id'),
                config('services.paypal.secret')
            )
        );

        $requestBody = $request->getContent();
        $webhookEvent = new WebhookEvent;
        $webhookEvent->fromJson($requestBody);
        Log::info($webhookEvent->getEventType());

        if ($webhookEvent->getEventType() == 'BILLING.SUBSCRIPTION.PAYMENT.FAILED') {

            Log::info(json_encode($webhookEvent->getResource(), true));
            $resource = json_decode($webhookEvent->getResource(), true);
            $last_date = Carbon::parse($resource['billing_info']['next_billing_time']);
            $last_date = ($last_date->addDays(10))->format('jS F');
            $subscription = Subscription::where('subscription_id', $resource['id'])->first();
            (new WebhookService)->failedPaymentMail($subscription, $last_date);

        } elseif ($webhookEvent->getEventType() == 'PAYMENT.SALE.COMPLETED') {
            Log::info(json_encode($webhookEvent->getResource(), true));
            $resource = json_decode($webhookEvent->getResource(), true);
            $last_date = Carbon::parse($resource['billing_info']['next_billing_time']);
            $subscription = Subscription::where('subscription_id', $resource['id'])->first();
            (new WebhookService)->failedPaymentMail($subscription);
        }
    }
}
