<?php

namespace App\Mail;

use App\Models\Plan;
use App\Models\Site;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BillingInfoMail extends Mailable
{
    use Queueable, SerializesModels;

    public $plan;

    public $site;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Plan $plan, Site $site)
    {
        $this->plan = $plan;
        $this->site = $site;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $site = $this->site;
        $plan = $this->plan;

        return $this->view('emails.billing_info', compact('site', 'plan'));
    }
}
