<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DebugOff extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:off';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will turn off debug mode.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $_ENV['APP_DEBUG'] = false;
        $this->call('config:cache');
        echo "Debug mode is turned off.\n";
        echo env('APP_DEBUG');
    }
}
