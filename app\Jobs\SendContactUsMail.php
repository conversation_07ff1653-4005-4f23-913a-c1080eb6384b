<?php

namespace App\Jobs;

use App\Mail\UserContactMail;
use App\Mail\UserContractConfirmationMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendContactUsMail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $details;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($details)
    {
        $this->details = $details;

        // dd(config('services.admin_mail'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $email = new UserContactMail($this->details);
        Mail::to(config('services.admin_mail'))->send($email);

        $customer_email = $this->details['email'];
        $confirmation_mail = new UserContractConfirmationMail($this->details);
        Mail::to($customer_email)->send($confirmation_mail);
    }
}
