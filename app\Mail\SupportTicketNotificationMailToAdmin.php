<?php

namespace App\Mail;

use App\Models\Ticket;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SupportTicketNotificationMailToAdmin extends Mailable
{
    use Queueable, SerializesModels;

    public $subject;

    public $message;

    public $senderMail;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $user, Ticket $ticket)
    {
        $this->senderMail = $user->email;
        $this->subject = "RE: $ticket->title (Ticket ID: $ticket->ticket_id)";
        $this->message = '<body><p>'.$ticket->message.' </p>
	    <p>Created by : '.$user->name.' </p>
        <p>Title : '.$ticket->title.'</p><p>Status: '.$ticket->status.'</p>
        <p>You can view the ticket at any time at <a href='.url('admin2022/support-tickets/'.$ticket->id).'>View Ticket Details</a></p></body>';

    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.ticket_comments')->from($this->senderMail)->subject($this->subject)->with('email_content', $this->message);
    }
}
