<?php

namespace App\Http\Controllers;

use App\Models\Plugin;
use App\Models\Theme;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DirectoryController extends Controller
{
    /**
     * common where like searching for themes and plugins
     *
     * @return mixed
     */
    private function whereLikeSearching($query, $keyword)
    {
        return $query->where('name', 'like', '%'.$keyword.'%')
            ->orWhere('developer', 'like', '%'.$keyword.'%')
            ->orWhere('description', 'REGEXP', $keyword);
    }

    /**
     * Show the Themes list
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function themes(Request $request)
    {
        $keyword = $request->input('keyword');
        $themes = Theme::with('versions', 'pageEditor');
        $themes->whereHas('versions', function ($query) {
            $query->limit(1);
        });

        if ($request->filled('keyword')) {
            $themes->where(function ($query) use ($keyword) {
                $this->whereLikeSearching($query, $keyword);
                $query->orWhereHas('pageEditor', function ($query) use ($keyword) {
                    $query->where('name', 'like', '%'.$keyword.'%');
                });
            });
        }
        $lists = $themes->paginate(15);

        $site_list = remaining_downloads();

        return view('directory.themes', compact('lists', 'site_list'));
    }

    /**
     * Show the Plugins list
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function plugins(Request $request)
    {
        $keyword = $request->input('keyword');
        $query = Plugin::with('versions')->whereHas('versions', function ($q) {
            $q->limit(1);
        });
        if ($request->filled('keyword')) {
            $query->where(function ($q) use ($keyword) {
                $this->whereLikeSearching($q, $keyword);
            });
        }
        $lists = $query->paginate(15);

        $site_list = remaining_downloads();

        return view('directory.plugins', compact('lists', 'site_list'));
    }

    /**
     * download the themes and plugins
     *
     * @return array|\Illuminate\Http\RedirectResponse
     */
    public function download(Request $request)
    {
        $inputs = $request->all();
        $user = Auth::user();
        $sites = auth()->user()->sites()->where('status', 'completed')->get();
        /*        $stop_cnt           = 0;
                $start_cnt          = 0;*/
        $max_download_limit = $sites->sum('download_limit');

        /*        foreach ($sites as $site) {
                    if ($site->status == 'stopped' || $site->status == 'deleted') {
                        $stop_cnt++;
                    } else {
                        $start_cnt++;
                    }
                }*/

        if (count($sites) > 0 && $max_download_limit > 0) {
            if (isset($inputs['item_type'], $inputs['item_id'], $inputs['item_version'])
                && $user->downloads < $max_download_limit) {
                $item_type = $inputs['item_type'];
                $item_id = (int) $inputs['item_id'];
                $item_version = (float) $inputs['item_version'];

                $itemClass = "App\Models\\".ucfirst($item_type).'Version';
                $item = false;
                try {
                    $item = $itemClass::find($item_version);
                } catch (\Throwable $th) {
                    return ['error' => $th->getMessage()];
                }

                if (! $item) {
                    return redirect()->back()->with('error', 'Item could not be found.');
                }

                $user->downloads++;
                $user->save();

                return redirect()->to($item->s3_url());
            } else {
                return redirect()->back()->with('error', 'You have exceded your monthly download limit.');
            }
        } else {
            return redirect()->back()->with('error', 'Please create a website to download items. <a href="https://codibu.com/sites/create"> Create a weibste</a>');
        }
    }
}
