<?php

namespace App\Http\Controllers;

use App\Country;
use App\Http\Requests\DnsValidation;
use App\Http\Requests\SiteCreate;
use App\Mail\UserWithProfessionalService;
use App\Models\AutomaticBackup;
use App\Models\AutomaticBackupPlan;
use App\Models\Category;
use App\Models\Demo;
use App\Models\Domain;
use App\Models\PageEditor;
use App\Models\PaymentPlatform;
use App\Models\Plan;
use App\Models\ProfessionalServicePlan;
use App\Models\ServiceCategories;
use App\Models\Site;
use App\Models\SiteType;
use App\Models\Style;
use App\Models\Subscription;
use App\Repository\AutomaticBackupRepository;
use App\Repository\PaymentHistoryRepository;
use App\Repository\ProfessionalServiceRepository;
use App\Repository\SubscriptionRepository;
use App\Resolver\PaymentPlatformResolver;
use App\Rules\PasswordFormet;
use App\Services\DomainChangeService;
use App\Services\LightsailService;
use App\Services\PaypalService;
use App\Services\StripeService;
use App\Services\ThemeFilter;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;
use phpseclib\Crypt\RSA;

class SiteController extends Controller
{
    protected $paymentPlatformResolver;

    public function __construct(PaymentPlatformResolver $paymentPlatformResolver)
    {
        $this->paymentPlatformResolver = $paymentPlatformResolver;
    }

    public function index()
    {
        $data['sites'] = auth()->user()->sites()
                        ->where(function($query) {
                            $query->whereHas('subscription', function($q) {
                                $q->where('next_billing_date', '>', now());
                            })->orWhereDoesntHave('subscription');
                        })
                        ->orderBy('id', 'desc')
                        ->paginate(15);
        $site = $data['sites']->first();
        $data['site'] = $site;
        if ($site) {
            $data['site'] = $site;
            /*$data['dnsInfo'] = $site->status ? (new LightsailService(auth()->user()->aws_account_id))->domainDetails($site->domain)['dns'] : 'failed';
            if ($site->domain_type) {
                $is_dns_added_to_domain =false;
                try {
                    $dnsr = dns_get_record($site->domain, DNS_A + DNS_NS);
                    foreach ($dnsr as $item) {
                        if (array_key_exists('target', $item)){
                            $arr[]=$item['target'];
                        }
                    }
                    $duplicates = collect($arr)->merge(collect($data['dnsInfo']))->duplicates();
                    if (count($duplicates)>1){
                        $is_dns_added_to_domain =true;
                    }
                } catch (\Exception $e){

                }
            }*/

            if (exec('echo $(dig +short '.$site->domain.' @*******)') != $site->server_ip) {
                $data['is_dns_added_to_domain'] = false;
            } else {
                $data['is_dns_added_to_domain'] = true;
            }

        }

        return view('sites.index', $data);
    }

    public function indexAjax()
    {
        $sites = Site::where('user_id', auth()->id())->get();
        foreach ($sites as $site) {
            $instance_name = $site->new_instance_name == null ? $site->instance_name : $site->new_instance_name;
            if ($site->status == 'stopping' || $site->status == 'starting') {
                $status = (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->getInstance($instance_name)['instance']['state']['name'];
                if ($site->status == 'starting' && $status == 'running') {
                    $site->status = 'completed';
                    $site->save();
                } elseif ($site->status == 'stopping' && $status == 'stopped') {
                    $site->status = 'stopped';
                    $site->save();
                }
            }
        }

        return Site::where('user_id', auth()->id())->get();
    }

    public function show($id)
    {
        $user = Auth::user();
        $site = $user->sites()->where('id', (int) $id)->firstOrFail();
        if ($site->status == 'terminated') {
            return redirect()->route('sites.edit', $site->id);
        }
        (new DomainChangeService)->domainPurchase($site);
        $data = [
            'plans' => Plan::with('specs')->get(),
            'payment_methods' => false,
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'intent' => false,
        ];

        //Start Automatic Backup
        if (auth()->user()->aws_account_id) {
            $backups1 = (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->getAutoSnapshots($site->new_instance_name);
            $backups2 = (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->getAutoSnapshots($site->instance_name);
            $backups = array_merge($backups1, $backups2);
            if (count($backups) > 7) {
                $backups = array_slice($backups, 0, 6);
            }

            if (session()->has('automatic_backup_subscription_id')) {
                $automaticBackup = AutomaticBackup::where('site_id', $site->id)->first();
                if (! $automaticBackup) {
                    $automaticBackup = (new AutomaticBackupRepository)->createAutomaticBackup($site);
                }

                (new SubscriptionRepository)->createSubscription('automatic_backup', $automaticBackup->id);
                (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->enableAutoSnapshot($automaticBackup->instance_name);
                /*$subscription = Subscription::where('subscription_id',session('automatic_backup_subscription_id'))->first();
                Mail::to(auth()->user())->queue(new UserWithProfessionalService($subscription, auth()->user()));*/
                session()->forget(codibu_session());
            }
        } else {
            $backups = [];
        }

        //End Automatic Backup

        // Default values
        $data['dnsInfo'] = 'failed';
        $data['dnsRecords'] = 'failed';

        // Check if site is active and AWS account ID exists
        if ($site->status && auth()->user()->aws_account_id !== null) {
            $domainDetails = (new LightsailService(
                auth()->user()->aws_account_id,
                $site->aws_region
            ))->domainDetails($site->domain);

            // Populate DNS info if response is valid
            if (is_array($domainDetails) && isset($domainDetails['dns'], $domainDetails['records'])) {
                $data['dnsInfo'] = $domainDetails['dns'];
                $data['dnsRecords'] = $domainDetails['records'];
            }
        }
        /*if ($site->domain_type) {
            $is_dns_added_to_domain =false;
            try {
                $dnsr = dns_get_record($site->domain, DNS_A + DNS_NS);
                foreach ($dnsr as $item) {
                    if (array_key_exists('target', $item)){
                        $arr[]=$item['target'];
                    }
                }
                $duplicates = collect($arr)->merge(collect($data['dnsInfo']))->duplicates();
                if (count($duplicates)>1){
                    $is_dns_added_to_domain =true;
                }
            } catch (\Exception $e){

            }
        }*/
        if (exec('echo $(dig +short '.$site->domain.' @*******)') != $site->server_ip) {
            $data['is_dns_added_to_domain'] = false;
        } else {
            $data['is_dns_added_to_domain'] = true;
        }

        //Amazon pay start
        /*$paymentPlatformAmazon = $this->paymentPlatformResolver->resolveServices('Amazon');
        $createCheckoutSession = $paymentPlatformAmazon->createCheckoutSession();
        $data['payload']       = $createCheckoutSession['payload'];
        $data['signature']     = $createCheckoutSession['signature'];*/
        //Amazon pay end
        $data['payment_methods'] = $user->payment_methods;
        $data['user'] = $user;
        $data['site'] = $site;
        $data['backups'] = $backups;

        return view('sites.single', $data);
    }

    public function edit(Request $request, $id)
    {
        $user = Auth::user();
        $site = $user->sites()->where('id', (int) $id)->firstOrFail();
        $subscription = $site->subscription;
        if (session()->exists('billing_subscription_id')) {
            if ((session('payment_platform') == 'PayPal' && $request->status == 'confirmed')) {
                $subscription->update([
                    'subscription_id' => session('billing_subscription_id'),
                    'status' => 'Active',
                    'platform' => session('payment_platform'),
                ]);
                (new PaymentHistoryRepository)->createPaymentHistory($subscription);
            } elseif (session('payment_platform') == 'Stripe') {
                $subscription->update([
                    'subscription_id' => session('billing_subscription_id'),
                    'status' => 'Active',
                    'platform' => session('payment_platform'),
                ]);
                (new PaymentHistoryRepository)->createPaymentHistory($subscription);
            }
            $site->status = 'starting';
            $site->save();
            (new LightsailService($user->aws_account_id, $site->aws_region))->startInstance($site);
        }
        session()->forget(codibu_session());
        if ($site->status != 'terminated') {
            return redirect()->route('sites.show', $site->id);
        }
        $data = [
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'payment_methods' => auth()->user()->payment_methods,
            'subscription' => $subscription,
            'site' => $site,
        ];

        return view('sites.edit', $data);
    }

    public function restoreDeletedSite(Request $request, $id)
    {
        $site = Site::find($id);
        $subscription = $site->subscription;
        session()->forget(codibu_session());
        $plan = $subscription->subscribable;

        $arr = array_merge($request->all(), [
            'billing_start_from' => $subscription->next_billing_date < date(now()) ? false : $subscription->next_billing_date,
            'change_billing_id' => $id,
            'paypal_plan_id' => $plan->paypal_plan_id,
            'stripe_plan_id' => $plan->stripe_plan_id,
            'restore_deleted_site' => true,
        ]);
        $arr = array_merge($arr);
        session()->put($arr);
        // payment start
        if ($request->payment_platform) {
            $paymentPlatform = $this->paymentPlatformResolver->resolveServices(session('payment_platform'));
            $paypal_url = '';
            if (session('payment_platform') == 'PayPal') {
                $paypal_url = $paymentPlatform->handleBillingSubscription();
            } elseif (session('payment_platform') == 'Stripe') {
                $paymentPlatform->handleBillingSubscription();
            }
        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }
        //payment end
        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => $request->payment_platform,
        ];

        return json_encode($data);
    }

    public function create(Request $request)
    {
        $data = [
            'is_phone' => (new Agent)->isPhone(),
            'site_types' => SiteType::pluck('name', 'name'),
            'styles' => Style::all(),
            'categories' => Category::all(),
            'plans' => Plan::with('specs')->get(),
            'professionalServiceCategories' => ServiceCategories::all(),
            'payment_methods' => false,
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'intent' => false,
            'demos' => Demo::pluck('name', 'name'),
            'editor_types' => PageEditor::where('id', '!=', 2)->where('id', '!=', 3)->pluck('name', 'id'),
            'selectedDemo' => session('selectedDemo') ? session('selectedDemo') : false,
            'backups' => null,
            'backupSiteId' => null,
            'backupDate' => null,
            'theme_type' => session('backupDate') && session('backupSiteId') ? 'backup' : 'demo',
        ];
        if ($data['theme_type'] == 'backup') {
            $site = Site::findOrFail(session('backupSiteId'));
            $diskSizeInGb = $site->subscription->subscribable->bundle->price;
            $data['plans'] = Plan::with('specs')->WhereHas('bundle', function ($query) use ($diskSizeInGb) {
                $query->where('price', '>=', $diskSizeInGb);
            })->get();

            $backups2 = (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->getAutoSnapshots($site->instance_name);
            $backups1 = (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->getAutoSnapshots($site->new_instance_name);
            $backups = array_merge($backups1, $backups2);

            $data['backups'] = $backups;
            $data['backupSiteId'] = session('backupSiteId');
            $data['backupDate'] = session('backupDate');
        }
        session()->forget(['selectedDemo', 'backupSiteId', 'backupDate']);
        $user = Auth::user();
        //Amazon pay start
        /*$paymentPlatformAmazon = $this->paymentPlatformResolver->resolveServices('Amazon');
        $createCheckoutSession = $paymentPlatformAmazon->createCheckoutSession();
        $data['payload']       = $createCheckoutSession['payload'];
        $data['signature']     = $createCheckoutSession['signature'];*/
        //Amazon pay end
        $data['payment_methods'] = $user->payment_methods;

        $data['user'] = $user;
        $data['countres'] = Country::all();

        if (! $data['selectedDemo']) {
            $randomIds = Demo::query()->pluck('id')->shuffle()->toArray();
            session(['random_ids' => $randomIds]);
        }
        //theme filtering from services class
        $data['themes'] = (new ThemeFilter)->filteringThemes($request);

        return view('sites.create', $data);
    }

    public function store(SiteCreate $request)
    {
        $request->validated();
        $plan = Plan::find(request('plan'));
        $serviceCategories = ServiceCategories::all();
        $service_plan_ids = [];
        foreach ($serviceCategories as $serviceCategory) {
            $service_plan_id = request('service_plan_for_cat_'.$serviceCategory->id);
            if ($service_plan_id) {
                $service_plan = ProfessionalServicePlan::find($service_plan_id);
                $service_plan_ids[] = $service_plan->id;
                $arr['service_plan_duration_'.$service_plan->id] = $service_plan->duration;
                $arr['stripe_service_plan_id_'.$service_plan->id] = $service_plan->stripe_plan_id;
                $arr['service_category_name_'.$service_plan->id] = $service_plan->category->name;
            }
        }

        $arr = array_merge($request->all(), [
            'paypal_plan_id' => $plan->paypal_plan_id,
            'stripe_plan_id' => $plan->stripe_plan_id,
            'order_no' => rand(1000, 9999).'-'.time(),
            'name' => 'Site',
        ]);

        if (request('domain_type') == 'transfer_domain') {
            cache()->add('authorization_code', request('authorization_code'));
        }

        $arr['domain_price'] = session('domain_price');
        $data = priceCalculation([
            'type' => 'site',
            'coupon_code' => request('coupon_code'),
            'plan_id' => request('plan'),
        ]);
        $arr = array_merge($arr, $data);
        session()->forget(codibu_session());
        session()->put($arr);
        if (count($service_plan_ids) > 0 && session('payment_platform') == 'Stripe') {

            $paymentPlatform = $this->paymentPlatformResolver->resolveServices(session('payment_platform'));

            foreach ($service_plan_ids as $service_plan_id) {
                $service_plan = ProfessionalServicePlan::find($service_plan_id);
                $arrr = [
                    'service_plan_duration' => $service_plan->duration,
                    'stripe_service_plan_id' => $service_plan->stripe_plan_id,
                    'service_category_name' => $service_plan->category->name,
                    'service_plan' => $service_plan_id,
                ];
                $data = priceCalculation([
                    'type' => 'professional_service',
                    'coupon_code' => false, //request('coupon_code'),
                    'plan_id' => $service_plan_id,
                ]);
                $arrr = array_merge($arrr, $data);
                session()->put($arrr);
                if ($service_plan->duration != 'one-time') {
                    $paymentPlatform->handleProfessionalServiceSubscription();
                    $service = (new ProfessionalServiceRepository)->createProfessionalService();
                    (new SubscriptionRepository)->createSubscription('professional_service', $service->id);
                    $subscription = Subscription::where('subscription_id', session('service_subscription_id'))->first();
                    Mail::to(auth()->user())->cc(['<EMAIL>'])->queue(new UserWithProfessionalService($subscription, auth()->user()));
                } else {
                    $paymentPlatform->createOneTimePayment();
                    if (session()->exists('order_id')) {
                        $service = (new ProfessionalServiceRepository)->createProfessionalService();
                        $subscription = (new SubscriptionRepository)->createSubscription('professional_service', $service->id);
                        Mail::to(auth()->user())->queue(new UserWithProfessionalService($subscription, auth()->user()));
                    }
                }

            }
            session()->put(['name' => 'Site']);
        }
        // payment start
        if ($plan->price < 1) {
            return json_encode([
                'status' => true,
                'payment_platform' => null,
            ]);
        } elseif (session('payment_platform')) {
            $paymentPlatform = session('payment_platform') != 'Amazon'
                ? $this->paymentPlatformResolver->resolveServices(session('payment_platform'))
                : '';
            $paypal_url = '';
            if (session('payment_platform') == 'PayPal') {
                $paypal_url = $paymentPlatform->handleSiteSubscription();
            } elseif (session('payment_platform') == 'Stripe') {
                $paymentPlatform->handleSiteSubscription();
            } else {
                session()->put('plan_id', $request->input('plan'));
            }
        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }
        //payment end
        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => session('payment_platform'),
        ];

        return json_encode($data);
    }

    public function update(Request $request, $id)
    {
        $plan = Plan::find(8);
        session()->put([
            'payment_platform' => $request->payment_platform,
            'payment_method' => $request->payment_method,
            'paypal_plan_id' => $plan->paypal_plan_id,
            'plan' => 8,
            'site_id' => $id,
            'total_price' => 6.99,
        ]);
        if ($request->payment_platform) {
            $paymentPlatform = $request->payment_platform != 'Amazon'
                ? $this->paymentPlatformResolver->resolveServices($request->payment_platform)
                : '';
            $paypal_url = '';
            if ($request->payment_platform == 'PayPal') {
                $paypal_url = $paymentPlatform->handleSiteSubscription();
            } elseif ($request->payment_platform == 'Stripe') {
                $paymentPlatform->handleSiteSubscription();
            } else {
                session()->put('plan_id', 8);
            }
        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }

        //payment end
        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => session('payment_platform'),
        ];

        return json_encode($data);
    }

    public function automaticBackup(Request $request, $id)
    {
        $site = Site::find($id);
        $automaticBackup = AutomaticBackup::where('site_id', $id)->first();
        if(($request->automatic_snapshot != 'on' &&  optional($automaticBackup->subscription)->status != 'Active') || ($request->automatic_snapshot == 'on' &&  optional($automaticBackup->subscription)->status == 'Active')) {
            //dd(123);
        } elseif ($request->automatic_snapshot != 'on' && $automaticBackup && optional($automaticBackup->subscription)->status == 'Active') {

            $validator = Validator::make($request->all(), [
                'automaticBackupOff' => 'required',
            ]);
            if ($validator->fails()) {
                return redirect(url()->previous() . '#tab-2')
                    ->withErrors($validator)
                    ->with('checkError', 1)
                    ->withInput();
            }
            

            $paymentPlatform = $this->paymentPlatformResolver->resolveServices($automaticBackup->subscription->platform);
            $paymentPlatform->cancelAutomaticBackupSubscription($automaticBackup->user->stripe_id, $automaticBackup->subscription->subscription_id);
            (new SubscriptionRepository)->cancelSubscription($automaticBackup->subscription->id);
            (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->disableAutoSnapshot($automaticBackup->instance_name);
            $data = [
                'status' => true,
                'msg' => 'canceled',
            ];
        } elseif (! $automaticBackup || ! $automaticBackup->subscription()->exists() || optional($automaticBackup->subscription)->status == 'Inactive') {
            $plan = AutomaticBackupPlan::first();
            session()->put([
                'payment_platform' => $request->payment_platform,
                'payment_method' => $request->payment_method,
                'paypal_plan_id' => $plan->paypal_plan_id,
                'stripe_plan_id' => $plan->stripe_plan_id,
                'plan' => $plan->id,
                'site_id' => $id,
                'total_price' => 5,
            ]);
            // payment start
            if ($request->payment_platform) {
                $paymentPlatform = $this->paymentPlatformResolver->resolveServices($request->payment_platform);
                $paypal_url = $paymentPlatform->handleAutomaticBackupSubscription();
                if ($request->payment_platform == 'PayPal') {
                    return redirect($paypal_url);
                }
            } else {
                return redirect()->back()->with(['errors' => ['payment' => 'Payment method not provided.']]);
            }
            //payment end
        }

        return redirect()->to(url()->previous() . '#tab-2');
    }

    public function resetForm()
    {
        session()->flush();

        return 0;
    }

    public function destroy($id)
    {
        $user = Auth::user();
        $site = $user->sites()->where('id', $id)->first();
        if ($site && $site->subscription && Carbon::parse($site->subscription->next_billing_date)->format('Y-m-d') > Carbon::now()->format('Y-m-d')) {
            $site->update(['status' => 'terminated']);
            try {
                (new LightsailService($site->user->aws_account_id, $site->aws_region))->stopInstance($site);
                if ($site->subscription->platform == 'Stripe') {
                    (new StripeService)->cancelSubscription(auth()->user()->stripe_id, $site->subscription->subscription_id);
                } else {
                    (new PaypalService)->cancelSubscription($site->subscription->subscription_id, 'change');
                }
                $site->subscription->update(['status' => 'Inactive']);
            } catch (\Throwable $th) {

            }
            (new SubscriptionRepository)->cancelSubscription($site->subscription->id);
        } else {
            $site->delete();
        }

        return redirect()->back()->with('success', 'Website termination request has been sent.');
    }

    public function stopInstance($id)
    {
        $user = Auth::user();
        $site = $user->sites()->where('id', (int) $id)->first();
        if ($site) {
            $site->status = 'stopping';
            $site->save();
            (new LightsailService($user->aws_account_id, $site->aws_region))->stopInstance($site);

            return redirect()->back()->with('success', 'Website Stop request has been sent.');
        } else {
            return redirect()->back()->with('success', 'Website Stop request Faild!.');
        }
    }

    public function startInstance($id)
    {
        $user = Auth::user();
        $site = $user->sites()->where('id', (int) $id)->first();
        if ($site->days >= 7 && ! $site->subscription) {
            return redirect()->back()->with('success', 'Please subscribe to start the instance. It will be terminated after 7 days and deleted after 14 days. To prevent this, it is necessary to subscribe');
        }
        if ($site) {
            $site->status = 'starting';
            $site->save();
            (new LightsailService($user->aws_account_id, $site->aws_region))->startInstance($site);

            return redirect()->back()->with('success', 'Website Start request has been sent.');
        } else {
            return redirect()->back()->with('success', 'Website Start request Faild!.');
        }
    }

    public function analysisDetails(Site $site)
    {
        return view('sites.analysis.index', compact('site'));
    }

    public function passwordValidation(Request $request)
    {
        $request->validate([
            'password' => ['required', new PasswordFormet],
        ]);

        return response()->json(['success' => 'Password is valid.']);
    }

    public function selectAutomaticBackup(Request $request)
    {
        return session()->put($request->all());
    }

    public function restoreSiteSession(Request $request)
    {
        Site::find($request->backupSiteId)->update(['status' => 'restoring']);

        return session()->put(['reStoring' => Carbon::now(), 'reStoringSiteId' => $request->backupSiteId], 2);
    }

    public function getRestoreSiteSession()
    {
        return response()->json(['reStoring' => session('reStoring'), 'reStoringSiteId' => session('reStoringSiteId')], 200);
    }

    public function restoreSite(Request $request)
    {
        session()->forget(['reStoring', 'reStoringSiteId']);
        $site = Site::findOrFail($request->backupSiteId);
        $aws_account_id = auth()->user()->aws_account_id;
        $instance = (new LightsailService($aws_account_id, $site->aws_region))->getInstance($site->instance_name);
        $new_instance = (new LightsailService($aws_account_id, $site->aws_region))->getInstance($site->new_instance_name);
        $site->new_instance_date = today();
        $site->backup_date = $request->backupDate;
        $site->backup_site_id = $site->id;
        if ($instance && ! $new_instance) {
            $site->max_restore = 1;
            $site->new_instance_name = $site->instance_name.'_'.$request->backupDate;
            if ($site->save()) {
                if ($instance['instance']['addOns'][0]['status'] != 'Disabled') {
                    (new LightsailService($aws_account_id, $site->aws_region))->disableAutoSnapshot($site->instance_name);
                }
                (new LightsailService($aws_account_id, $site->aws_region))->createInstancesFromSnapshot($site,$site->instance_name, true);
                sleep(60);
                $staticIp = (new LightsailService($aws_account_id, $site->aws_region))->getStaticIp($site->static_ip_name);
                $new_instance = (new LightsailService($aws_account_id, $site->aws_region))->getInstance($site->new_instance_name);

                if ($staticIp['staticIp']['isAttached']) {
                    (new LightsailService($aws_account_id, $site->aws_region))->detachStaticIp($site->static_ip_name);
                }

                if ($new_instance['instance']['state']['name'] == 'running') {
                    (new LightsailService($aws_account_id, $site->aws_region))->attachStaticIp($site->new_instance_name, $site->static_ip_name);
                    if (optional(optional($site->automaticBackup)->subscription)->status == 'Active') {
                        (new LightsailService($aws_account_id, $site->aws_region))->enableAutoSnapshot($site->new_instance_name);
                    }
                }
            }
        } elseif ($instance && $new_instance) {

            $backups = (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->getAutoSnapshots($site->new_instance_name);
            
            if (count($backups) < 1) {
                $instanceToDelete = false;
                (new LightsailService($aws_account_id, $site->aws_region))->deleteInstance($site->new_instance_name);
            } else {
                $hasDate = collect($backups)->contains(function ($item) use ($site) {
                    return $item['date'] === $site->backup_date;
                });
                
                if( $hasDate ){
                    $sourceInstanceName = $site->backupSite->new_instance_name;
                } else {
                    $sourceInstanceName = $site->backupSite->instance_name;
                }
                
                $instanceToDelete = $site->instance_name;
                $site->instance_name = $site->new_instance_name;
            }
            if ($site->new_instance_name != explode('_', $site->new_instance_name)[0].'_'.$request->backupDate) {
                $site->new_instance_name = explode('_', $site->new_instance_name)[0].'_'.$request->backupDate;
            } // else {
            //     $site->update(['status' => 'completed']);

            //     return \response()->json(['status' => 'failed', 'msg' => 'This backup is already restored'], 500);
            // }
            if ($site->save()) {
                if ($instance['instance']['addOns'][0]['status'] != 'Disabled') {
                    (new LightsailService($aws_account_id, $site->aws_region))->disableAutoSnapshot($site->instance_name);
                }

                (new LightsailService($aws_account_id, $site->aws_region))->createInstancesFromSnapshot($site, $sourceInstanceName, true);
                sleep(60);
                $staticIp = (new LightsailService($aws_account_id, $site->aws_region))->getStaticIp($site->static_ip_name);
                $new_instance = (new LightsailService($aws_account_id, $site->aws_region))->getInstance($site->new_instance_name);

                if ($staticIp['staticIp']['isAttached']) {
                    (new LightsailService($aws_account_id, $site->aws_region))->detachStaticIp($site->static_ip_name);
                }

                if ($new_instance['instance']['state']['name'] == 'running') {
                    (new LightsailService($aws_account_id, $site->aws_region))->attachStaticIp($site->new_instance_name, $site->static_ip_name);
                    if (optional(optional($site->automaticBackup)->subscription)->status == 'Active') {
                        (new LightsailService($aws_account_id, $site->aws_region))->enableAutoSnapshot($site->new_instance_name);
                    }
                }
                
                if($instanceToDelete) {
                    (new LightsailService($aws_account_id, $site->aws_region))->deleteInstance($instanceToDelete);
                }
            }
        }
        $site->update(['status' => 'completed']);

        return \response()->json(['status' => 'success', 'msg' => $instanceToDelete], 200);
    }

    public function downloadKey(Request $request)
    {
        $site = Site::findOrFail($request->siteId);
        if (auth()->id() == $site->user_id) {
            $pem = storage_path('app/temp/access_key.pem');
            $ppk = storage_path('app/temp/access_key.ppk');

            if (Storage::exists('temp/access_key.pem')) {
                Storage::delete('temp/access_key.pem');
            }

            if (Storage::exists('temp/access_key.ppk')) {
                Storage::delete('temp/access_key.ppk');
            }
            if ($request->fileType == 'pem') {
                file_put_contents($pem, $site->private_key);

                return response()->download($pem);
            } else {
                $rsa = new RSA;
                $rsa->loadKey($site->private_key);
                $ppkPrivateKey = $rsa->getPrivateKey(RSA::PRIVATE_FORMAT_PUTTY);
                file_put_contents($ppk, $ppkPrivateKey);

                return response()->download($ppk);
            }
        }
    }

    public function storeOrUpdateDnsRecord(DnsValidation $request, $id)
    {
        $site = Site::find($id);
        if (! $request->record_id) {
            return (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->addDnsRecord($request->all(), $id);
        } else {
            return (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->updateDnsRecord($request->all(), $id);
        }
    }

    public function deleteDnsRecord(Request $request, $id)
    {
        $site = Site::find($id);
        (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->deleteDnsRecord($request->record_id, $id);
    }
}
