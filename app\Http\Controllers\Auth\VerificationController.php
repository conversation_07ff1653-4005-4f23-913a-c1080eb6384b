<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\AwsService;
use App\User;
use DB;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;

class VerificationController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Email Verification Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling email verification for any
    | user that recently registered with the application. Emails may also
    | be re-sent if the user didn't receive the original email message.
    |
    */

    use VerifiesEmails;

    /**
     * Where to redirect users after verification.
     *
     * @var string
     */
    protected $redirectTo = '/sites';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('signed')->only('verify');
        $this->middleware('throttle:6,1')->only('verify', 'resend');
    }

    /**
     * Mark the authenticated user's email address as verified.
     *
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function verify(Request $request)
    {

        $user = User::findOrFail($request->input('id'));
        if ($user->hasVerifiedEmail()) {
            return redirect('/login')->with('success', 'Your email is already verified.');
        }
        $user->email_verified_at = now();
        $user->save();
        (new AwsService)->createAccount($user);
        DB::commit();

        return redirect('/login')->with('success', 'Your email is verified. You can now login.');

    }

    public function verifyNotice()
    {
        $email = session()->has('email') ? session()->get('email') : old('email');
        if (! $email) {
            return redirect('/login')->with('warning', 'Invalid email.');
        }

        return view('auth.verify', compact('email'));
    }

    /**
     * Resend the email verification notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function verifyNoticeResend($email)
    {
        $email = Crypt::decrypt($email);

        if (! $email) {
            return redirect('/login')->with('warning', 'Invalid email.');
        }

        $user = User::where('email', $email)->first();

        if ($user->hasVerifiedEmail()) {
            return redirect('/login')->with('success', 'Your email address has already been verified.');
        }
        session()->forget('email');
        session()->flash('resent', $email);
        $user->sendEmailVerificationNotification();

        return redirect('/login')->with('success', 'Verification email has been sent to your email address.');
    }
}
