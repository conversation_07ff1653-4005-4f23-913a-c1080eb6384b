<?php

namespace App\Http\Requests;

use App\Rules\MatchOldPassword;
use App\Rules\PasswordFormet;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class UserProfile extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        if (! empty($request->current_password) || ! empty($request->new_password)) {
            if (auth()->user()->password) {
                $rules['current_password'] = ['required', new MatchOldPassword];
            }
            $rules['new_password'] = ['required', new PasswordFormet];
            $rules['new_confirm_password'] = [new PasswordFormet, 'same:new_password'];
        }
        $rules['name'] = 'required|min:3';
        $rules['email'] = 'required|email|unique:users,email,'.auth()->id().',id';
        $rules['phone'] = '';
        $rules['street_address'] = '';
        $rules['city'] = '';
        $rules['state'] = '';
        $rules['zip'] = '';

        return $rules;
    }
}
