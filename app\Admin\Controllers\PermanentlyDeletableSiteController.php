<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Site\Payment\PartialRefund;
use App\Admin\Actions\Site\Payment\Refund;
use App\Admin\Actions\Site\PermanentlyDelete;
use App\Admin\Actions\Site\Restore;
use App\Models\Site;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class PermanentlyDeletableSiteController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Deletable Site';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Site);
        $grid->model()->where('status', 'terminated');

        $grid->column('id', __('Id'));
        //$grid->column('title', __('Title'));
        //$grid->column('slug', __('Slug'));
        $grid->column('domain', __('Domain'));
        $grid->column('server_ip', __('IP'));
        $grid->column('status')->using(['terminated' => 'Terminated', 'completed' => 'Active', 'building' => 'Building', 'stopped' => 'Stopped']);
        $grid->column('subscription.status', __('Subscription Status'));
        $grid->column('subscription.next_billing_date', __('Next Billing Date'))->display(function ($date) {
            $badgeColor = Carbon::parse($date)->isPast() ? 'red' : 'green'; // Check if the date is in the past

            return "<span class='badge' style='background-color: {$badgeColor};'>{$date}</span>";
        });
        //$grid->column('created_at', __('Created at'));
        //$grid->column('updated_at', __('Updated at'));
        $grid->actions(function ($actions) {
            $actions->add(new Restore);
            $actions->add(new PermanentlyDelete);
            $actions->disableDelete();
            $actions->disableEdit();
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Site::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('title', __('Title'));
        $show->field('slug', __('Slug'));
        $show->field('domain', __('Domain'));
        $show->field('email', __('Email'));
        $show->field('status')->using(['deleted' => 'Termineted', 'completed' => 'Active']);
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));
        $show->user('User Details', function ($user) {
            $user->field('id', __('Id'));
            $user->field('name', __('Name'));
            $user->field('email', __('Email'));
            $user->field('downloads', __('Downloads'));
            $user->panel()
                ->tools(function ($tools) {
                    $tools->disableEdit();
                    $tools->disableList();
                    $tools->disableDelete();
                });
        });
        $show->plan('Plan Details', function ($plan) {
            $plan->field('id', __('Id'));
            $plan->field('name', __('Name'));
            $plan->field('description', __('Description'));
            $plan->field('duration', __('Duration'));
            $plan->field('price', __('Price'));
            $plan->panel()
                ->tools(function ($tools) {
                    $tools->disableEdit();
                    $tools->disableList();
                    $tools->disableDelete();
                });
        });
        $show->paymentHistories('Payment History', function ($payment) {
            $payment->id();
            $payment->platform('Payment Platform');
            $payment->payment_amount('Paid Amount ($)');
            $payment->refund_amount('Refund Amount ($)');
            $payment->created_at('Paid On')->display(function ($createdAt) {
                return Carbon::parse($createdAt)->format('m/d/Y h:i a');
            });

            $payment->disablePagination();

            $payment->disableCreateButton();

            $payment->disableFilter();

            $payment->disableRowSelector();

            $payment->disableColumnSelector();

            $payment->disableTools();

            $payment->disableExport();

            $payment->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->disableEdit();
                $actions->disableDelete();
                $actions->add(new Refund);
                $actions->add(new PartialRefund);
            });

        });
        $show->theme('Theme Details', function ($theme) {
            $theme->field('id', __('Id'));
            $theme->field('name', __('Name'));
            $theme->field('slug', __('Slug'));
            $theme->field('pageEditor.name', __('Page Editor'));
            $theme->panel()
                ->tools(function ($tools) {
                    $tools->disableEdit();
                    $tools->disableList();
                    $tools->disableDelete();
                });
            $theme->versions('Theme Versions', function ($versions) {
                $versions->resource('/admin/versions');
                $versions->id();
                $versions->version();
                $versions->download_url();
                $versions->created_at()->display(function ($createdAt) {
                    return Carbon::parse($createdAt)->format('m/d/Y h:i a');
                });
                $versions->filter(function ($filter) {
                    $filter->like('version');
                });
                $versions->panel()
                    ->tools(function ($tools) {
                        $tools->disableEdit();
                        $tools->disableList();
                        $tools->disableDelete();
                    });
                $versions->disableActions();

                $versions->disablePagination();

                $versions->disableCreateButton();

                $versions->disableFilter();

                $versions->disableRowSelector();

                $versions->disableColumnSelector();

                $versions->disableTools();

                $versions->disableExport();

                $versions->actions(function (Grid\Displayers\Actions $actions) {
                    $actions->disableView();
                    $actions->disableEdit();
                    $actions->disableDelete();
                });
            });
        });
        $show->panel()
            ->tools(function ($tools) {
                $tools->disableEdit();
                $tools->disableList();
                $tools->disableDelete();
            });

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Site);

        $form->text('title', __('Title'));
        $form->text('slug', __('Slug'));
        $form->text('billing_name', __('Billing name'));
        $form->textarea('billing_address', __('Billing address'));
        $form->text('domain', __('Domain'));
        $form->text('domain_type', __('Domain type'));
        $form->decimal('domain_price', __('Domain price'));
        $form->email('email', __('Email'));
        $form->text('server_ip', __('Server ip'));
        $form->text('private_key_name', __('Private key name'));
        $form->textarea('private_key', __('Private key'));
        $form->text('route_id', __('Route id'));
        $form->text('old_server_ip', __('Old server ip'));
        $form->text('admin_password', __('Admin password'));
        $form->text('db_pass', __('Db pass'));
        $form->text('status', __('Status'));
        $form->text('theme_type', __('Theme type'));
        $form->text('stack_id', __('Stack id'));
        $form->number('theme_id', __('Theme id'));
        $form->number('demo_id', __('Demo id'));
        $form->number('backup_site_id', __('Backup site id'));
        $form->date('backup_date', __('Backup date'))->default(date('Y-m-d'));
        $form->number('user_id', __('User id'));
        $form->text('instance_name', __('Instance name'));
        $form->text('new_instance_name', __('New instance name'));
        $form->date('new_instance_date', __('New instance date'))->default(date('Y-m-d'));
        $form->number('max_restore', __('Max restore'));
        $form->text('static_ip_name', __('Static ip name'));
        $form->text('order_no', __('Order no'));
        $form->switch('ssl_installed', __('Ssl installed'));
        $form->textarea('dns_list', __('Dns list'));
        $form->switch('ini_update_dns_check', __('Ini update dns check'));

        return $form;
    }
}
