<?php

namespace App\Mail;

use App\Models\ProfessionalServicePlan;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class InvoiceMail extends Mailable
{
    use Queueable, SerializesModels;

    public $professionalService;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($professionalServiceId)
    {
        $this->professionalService = ProfessionalServicePlan::find($professionalServiceId);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->subject = 'Codibu Invoice';

        return $this->markdown('emails.invoice')->from('<EMAIL>')->subject($this->subject)->with('professionalService', $this->professionalService);
    }
}
