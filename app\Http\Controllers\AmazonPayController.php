<?php

namespace App\Http\Controllers;

use App\Resolver\PaymentPlatformResolver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AmazonPayController extends Controller
{
    /**
     * @var PaymentPlatformResolver
     */
    protected $paymentPlatformResolver;

    /**
     * @var \Illuminate\Contracts\Foundation\Application|mixed
     */
    protected $paymentPlatformAmazon;

    public function __construct(PaymentPlatformResolver $paymentPlatformResolver)
    {
        $this->paymentPlatformResolver = $paymentPlatformResolver;
        $this->paymentPlatformAmazon = $this->paymentPlatformResolver->resolveServices('Amazon');
    }

    public function merchantReviewPage()
    {
        $checkoutSessionId = request('amazonCheckoutSessionId');
        session()->put('amazonCheckoutSessionId', $checkoutSessionId);
        if (session()->exists('payment_platform')) {
            return view('amazon.paywithamazonpay');
        } else {
            return redirect()->back()->with('error', 'Site data not found! please try again');
        }
    }

    public function paymentUpdate()
    {
        if (session()->exists('amazonCheckoutSessionId')) {
            $checkoutSessionId = session()->get('amazonCheckoutSessionId');
            try {
                $result = $this->paymentPlatformAmazon->getCheckoutSession($checkoutSessionId);

                if ($result['status'] === 200) {

                    return redirect($this->paymentPlatformAmazon->updateCheckoutSession($checkoutSessionId));
                } else {
                    // check the error
                    echo 'status='.$result['status'].'; response='.$result['response']."\n";
                }

                return view('merchant-review-page');
            } catch (\Exception $e) {
                // handle the exception
                echo $e."\n";
            }
        }

        return redirect($this->paymentPlatformAmazon->updateCheckoutSession($checkoutSessionId));
    }

    public function merchantConfirmPage()
    {
        session()->put('subscription_id', request('amazonCheckoutSessionId'));

        return redirect()->route('thankYou');
    }

    public function handleNotifications(Request $request)
    {
        Log::info('Amazon Test');
        $payload = $request->getContent();
        Log::info($payload);
    }
}
