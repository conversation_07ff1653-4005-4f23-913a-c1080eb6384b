<?php

namespace App\Console\Commands;

use App\Models\Domain;
use App\Services\Route53DomainsService;
use Illuminate\Console\Command;

class DomainRenew extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'toprankon:domainAutoRenew';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //maybe we do not need this code
        /*
        foreach (Domain::all() as $domain){
            if ($domain->expire == date("Y-m-d") && !$domain->renew_auto) {
                (new Route53DomainsService())->deleteRegisteredDomains($domain->domain);
            };
        }*/
    }
}
