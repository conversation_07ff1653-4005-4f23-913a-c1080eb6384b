<?php

namespace App\Jobs;

use App\Models\Site;
use App\Models\Subscription;
use App\Services\LightsailService;
use App\Services\PaypalService;
use App\Services\StripeService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CheckAutomaticCancelSubscriptions implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //stop services that's are automatically cancel subscriptions
        Subscription::where(['status' => 'Active'])
            ->where('subscription_id', 'not LIKE', '%sched%')
            ->whereHasMorph('serviceable', [\App\Models\Site::class])
            ->whereDate('next_billing_date', '<', Carbon::now()->toDateString())
            ->orderBy('id')
            ->chunk(5, function ($subscriptions) {
                foreach ($subscriptions as $subscription) {
                    if ($subscription->platform == 'Stripe') {
                        $subscriptiondetails = (new StripeService)->getSubscription($subscription);
                        if ($subscriptiondetails['status'] == 'canceled') {
                            $this->stopInstance($subscription);
                        }
                    } elseif ($subscription->platform == 'PayPal') {
                        $subscriptiondetails = (new PaypalService)->getSubscriptionDetails($subscription->subscription_id);
                        if ($subscriptiondetails['status'] == 'CANCELLED') {
                            $this->stopInstance($subscription);
                        }
                    }
                }
            });

        //start services that's are reactive subscriptions
        Subscription::where(['status' => 'Inactive'])
            ->where('subscription_id', 'not LIKE', '%sched%')
            ->whereHasMorph('serviceable', [\App\Models\Site::class])
            ->whereDate('next_billing_date', '<', Carbon::now()->toDateString())
            ->orderBy('id')
            ->chunk(5, function ($subscriptions) {
                foreach ($subscriptions as $subscription) {
                    if ($subscription->platform == 'Stripe') {
                        $subscriptiondetails = (new StripeService)->getSubscription($subscription);
                        if ($subscriptiondetails['status'] != 'canceled') {
                            $this->startInstance($subscription);
                        }
                    } elseif ($subscription->platform == 'PayPal') {
                        $subscriptiondetails = (new PaypalService)->getSubscriptionDetails($subscription->subscription_id);
                        if ($subscriptiondetails['status'] != 'CANCELLED') {
                            $this->startInstance($subscription);
                        }
                    }
                }
            });
    }

    public function stopInstance($subscription)
    {
        $subscription->update(['status' => 'Inactive']);
        $site = Site::where('id', $subscription->serviceable_id)->first();
        if ($site) {
            $site->status = 'terminated';
            $site->save();
            (new LightsailService($site->user->aws_account_id, $site->aws_region))->stopInstance($site);

            return redirect()->back()->with('success', 'Website Stop request has been sent.');
        } else {
            return redirect()->back()->with('success', 'Website Stop request Faild!.');
        }
    }

    public function startInstance($subscription)
    {
        $subscription->update(['status' => 'Active']);
        $site = Site::where('id', $subscription->serviceable_id)->first();
        if ($site) {
            $site->status = 'completed';
            $site->save();
            (new LightsailService($site->user->aws_account_id, $site->aws_region))->startInstance($site);

            return redirect()->back()->with('success', 'Website start request has been sent.');
        } else {
            return redirect()->back()->with('success', 'Website start request Faild!.');
        }
    }
}
