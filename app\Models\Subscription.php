<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'active_until' => 'datetime',
    ];

    protected $appends = ['service_type'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /*    public function plan()
        {
            return $this->belongsTo(Plan::class);
        }*/

    public function getServiceTypeAttribute()
    {
        $value = explode('\\', $this->serviceable_type)[2];
        if ($value == 'ProfessionalService') {
            $value = optional($this->subscribable)->custom_invoice == null ? 'Professional Service' : 'Custom invoice';
        } elseif ($value == 'AutomaticBackup') {
            $value = 'Automatic Backup';
        }

        return $value;
    }

    public function subscribable()
    {
        return $this->morphTo();
    }

    public function serviceable()
    {
        return $this->morphTo();
    }

    public function paymentHistories()
    {
        return $this->hasMany(PaymentHistory::class)->orderBy('end_date', 'desc');
    }

    public function latestPaymentHistory()
    {
        return $this->hasOne(PaymentHistory::class)->latest();
    }
}
