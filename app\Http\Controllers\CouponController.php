<?php

namespace App\Http\Controllers;

use App\Models\Coupon;
use Illuminate\Http\Request;

class CouponController extends Controller
{
    /**
     * check the coupon
     */
    public function checkCoupon(Request $request): \Illuminate\Http\JsonResponse
    {
        if (! request('coupon_code')) {
            return response()->json();
        }

        $data = priceCalculation($request->all());
        $data['price'] = $data['total_price'];
        $data['discount'] = $data['coupon_discount'];

        return response()->json($data);
    }

    /**
     * Store a newly created coupon in the database.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'coupon_code' => 'required|string|unique:coupons,coupon_code',
            'discount_percentage' => 'required|numeric|min:0|max:100',
            'expire_date' => 'required|date',
            'is_recurring' => 'sometimes|boolean',
        ]);

        Coupon::create([
            'coupon_code' => $request->coupon_code,
            'discount_percentage' => $request->discount_percentage,
            'expire_date' => $request->expire_date,
            'is_recurring' => $request->has('is_recurring'), // "매달 반복 적용" 체크 여부 저장
        ]);

        return redirect()->route('admin.coupons.index')->with('success', '쿠폰이 성공적으로 생성되었습니다.');
    }
}
