<?php

namespace App\Http\Requests;

use App\Rules\DomainPriceLimit;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class PurchaseDomain extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        return [
            'domain' => ['required', 'unique:sites,domain', new DomainPriceLimit('purchase_domain')],
        ];
    }
}
