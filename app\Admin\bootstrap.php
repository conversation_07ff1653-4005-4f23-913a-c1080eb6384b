<?php

/**
 * <PERSON><PERSON>-admin - admin builder based on <PERSON><PERSON>.
 *
 * <AUTHOR> <https://github.com/z-song>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 * Encore\Admin\Form::forget(['map', 'editor']);
 *
 * Or extend custom form field:
 * Encore\Admin\Form::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 */
Encore\Admin\Form::forget(['map', 'editor']);

Admin::style('.col-md-12 > .select2 {display: none !important;}');
Admin::style('.col-md-12 > .input-group {display: none !important;}');
