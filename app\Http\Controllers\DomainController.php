<?php

namespace App\Http\Controllers;

use App\Models\Domain;
use App\Models\DomainRenewPlan;
use App\Models\PaymentPlatform;
use App\Repository\SubscriptionRepository;
use App\Resolver\PaymentPlatformResolver;
use App\Services\Route53DomainsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DomainController extends Controller
{
    /**
     * @var ;
     */
    protected $paymentPlatformResolver;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(PaymentPlatformResolver $paymentPlatformResolver)
    {
        $this->paymentPlatformResolver = $paymentPlatformResolver;
    }

    public function index()
    {
        if (session()->has('transfer_domain_subscription_id')) {

            $domain = Domain::create([
                'domain' => session('domain'),
                'user_id' => Auth::id(),
                'price' => session('transfer_domain_price'),
            ]);

            if (session('payment_platform') == 'PayPal' && request('status') == 'confirmed') {
                (new SubscriptionRepository)->createSubscription('transfer_domain', $domain->id);
            } elseif (session('payment_platform') == 'Stripe') {
                (new SubscriptionRepository)->createSubscription('transfer_domain', $domain->id);
            }
            session()->forget(codibu_session());
        }

        $domains = Domain::where('user_id', auth()->id())->get();

        return view('domains.index')->with('domains', $domains);
    }

    public function edit($domain_name)
    {
        $domainModel = Domain::where('domain', $domain_name)->firstOrfail();

        if (session()->has('domain_subscription_id')) {
            if (session('payment_platform') == 'PayPal' && request('status') == 'confirmed') {
                (new SubscriptionRepository)->createSubscription('domain', $domainModel->id);
            } elseif (session('payment_platform') == 'Stripe') {
                (new SubscriptionRepository)->createSubscription('domain', $domainModel->id);
            }
        }
        session()->forget(codibu_session());

        $domain = (new Route53DomainsService)->purchasedDomainDetails($domain_name);
        $domain['renew_auto'] = 0;
        $authorizationCode = (new Route53DomainsService)->domainAuthorizationCode($domain_name);
        if($domain['errorCode'] != 'InvalidInput'){
            $domain['created_at'] = formatted_date($domain['CreationDate']);
            $domain['expires'] = formatted_date($domain['ExpirationDate']);
            $domain['renew_deadline'] = formatted_date($domain['ExpirationDate']);
            $domain['renew_auto'] = $domain['AutoRenew'] ? 1 : 0;
        }

        $data = [
            'domain' => $domain,
            'authorizationCode' => $authorizationCode,
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'payment_methods' => auth()->user()->payment_methods,
        ];

        return view('domains.edit')->with($data);
    }

    public function updateRenew(Request $request, $domain_name)
    {
        $domain = Domain::where('domain', $domain_name)->first();
        if ($request->renew_auto != 'on' && optional($domain->subscription)->status == 'Active') {
            $paymentPlatform = $request->payment_platform != 'Amazon'
                ? $this->paymentPlatformResolver->resolveServices($domain->subscription->platform)
                : '';
            $paymentPlatform->cancelDomainSubscription($domain->subscription->subscription_id);
            (new SubscriptionRepository)->cancelSubscription($domain->subscription->id);
            (new Route53DomainsService)->disableDomainAutoRenew($domain_name);
            $data = [
                'status' => true,
                'msg' => 'canceled',
            ];
        } elseif (! $domain->subscription()->exists() || optional($domain->subscription)->status == 'Inactive') {
            $start_from = (new Route53DomainsService)->purchasedDomainDetails($domain_name)['ExpirationDate'];
            $domain_plan = DomainRenewPlan::where('price', $domain['price'])->first();
            $arr = array_merge($request->all(), [
                'domain' => $domain_name,
                'paypal_plan_id' => $domain_plan->paypal_plan_id,
                'stripe_plan_id' => $domain_plan->stripe_plan_id,
                'start_from' => $start_from,
                'plan' => $domain_plan->id,
            ]);
            session()->put($arr);
            // payment start
            if ($request->payment_platform) {
                $paymentPlatform = $request->payment_platform != 'Amazon'
                    ? $this->paymentPlatformResolver->resolveServices(session('payment_platform'))
                    : '';
                $paypal_url = '';
                if ($request->payment_platform == 'PayPal') {
                    $paypal_url = $paymentPlatform->handleDomainSubscription();
                } elseif ($request->payment_platform == 'Stripe') {
                    $paymentPlatform->handleDomainSubscription();
                } else {
                    session()->put('plan_id', $request->input('plan'));
                }
            } else {
                return ['errors' => ['payment' => 'Payment method not provided.']];
            }
            //payment end

            $data = [
                'status' => true,
                'paypal_url' => $paypal_url,
                'payment_platform' => $request->payment_platform,
            ];
        }

        return json_encode($data);
    }

    public function updateLock(Request $request, $domain)
    {
        (new Route53DomainsService)->updateDomainTransferLock($domain, $request->locked);

        $authorizationCode = (new Route53DomainsService)->domainAuthorizationCode($domain);

        return response()->json(['authorizationCode' => $authorizationCode, 'locked' => $request->locked], 200);
    }

    public function updateDns(Request $request, $domain)
    {
        $this->validate($request, [
            'dns_zone_one' => 'required',
            'dns_zone_two' => 'required',
            'dns_zone_three' => 'required',
            'dns_zone_four' => 'required',
        ]);

        $result = (new Route53DomainsService)->purchasedDomainDnsUpdate([
            ['Name' => request('dns_zone_one')],
            ['Name' => request('dns_zone_two')],
            ['Name' => request('dns_zone_three')],
            ['Name' => request('dns_zone_four')],
        ], $domain);
        session()->flash('status', 'DNS Servers Successfully Updated.');

        return redirect()->route('domains.edit', $domain);
    }

    public function check_domain(Request $request)
    {
        $this->validate($request, [
            'query' => 'required',
        ], [
            'required' => 'The domain field is required.',
        ]);

        $results = collect((new Route53DomainsService)->searchDomains(request('query')));
        return response()->json($results);
    }
}
