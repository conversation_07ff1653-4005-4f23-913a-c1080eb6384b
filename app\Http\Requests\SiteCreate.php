<?php

namespace App\Http\Requests;

use App\Rules\CheckStripeCardExpire;
use App\Rules\CheckTrail;
use App\Rules\CreateDnsZone;
use App\Rules\DomainPriceLimit;
use App\Rules\PasswordFormet;
use App\Rules\SiteCreateLimit;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class SiteCreate extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        if ($request->theme_type == 'demo') {
            return [
                'site_create_limit' => new SiteCreateLimit,
                'demo_id' => 'required',
                'aws_region' => 'required',
                'title' => 'required',
                'password' => ['required', new PasswordFormet],
                'domain' => ['sometimes', 'nullable', 'unique:sites,domain', new DomainPriceLimit(request('domain_type'))],
                'plan' => ['required', new CheckTrail(request('domain_type')), new CheckStripeCardExpire($request)],
                'domain_type' => ['required', new CreateDnsZone],
            ];
        } else {
            return [
                'site_create_limit' => new SiteCreateLimit,
                'backupDate' => 'required',
                'backupSiteId' => 'required',
                'aws_region' => 'required',
                'title' => 'required',
                'password' => ['required', new PasswordFormet],
                'domain' => ['sometimes', 'nullable', 'unique:sites,domain', new DomainPriceLimit(request('domain_type'))],
                'plan' => ['required', new CheckTrail(request('domain_type'))],
                'domain_type' => ['required', new CreateDnsZone],
            ];
        }
    }
}
