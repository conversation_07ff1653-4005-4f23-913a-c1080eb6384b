<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use phpseclib\Crypt\RSA;
use phpseclib\Net\SSH2;

class ExportDB implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $demos;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($demos)
    {
        $this->demos = $demos;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->demos->each(function ($demo, $key) {
            $key3 = new RSA;
            $key3->loadKey(file_get_contents(storage_path('app/be.pem')));
            $ssh3 = new SSH2('**************');

            if (! $ssh3->login('bitnami', $key3)) {
                throw new Exception('Login Failed', E_WARNING);
            } else {
                $blog_prefix = $demo->blog_prefix;
                $dsfsd = $ssh3->exec('cd /opt/bitnami/wordpress/wp-content/uploads/sites/'.$blog_prefix.' && zip -r /opt/bitnami/wordpress/wp-content/uploads/sites2/'.$blog_prefix.'.zip .');

                /*$blog_prefix = $demo->blog_prefix;
                $asd = $ssh3->exec("cd /opt/bitnami/wordpress && sudo wp db tables 'wp_". $blog_prefix ."_*' --all-tables");
                $sdfsdf = ltrim(rtrim(str_replace("\\n",",",json_encode($asd)),',"'),'",');
                $asdqwe = "cd /opt/bitnami/wordpress && sudo wp db export --tables=". $sdfsdf . " ./tmp/".$demo->url . ".sql --allow-root";
                $ssh3->exec($asdqwe);*/
            }
        });
    }
}
