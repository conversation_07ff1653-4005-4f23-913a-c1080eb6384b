<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Repository\SiteCreateRepository;
use App\Services\LightsailService;

class ThankYouController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    public function __invoke()
    {
        if (session()->has('subscription_id') || (session()->has('plan_price') && session('plan_price') < 1)) {
            $site = (new SiteCreateRepository)->createSite();
            $plan = Plan::find(session('plan'));
            $sourceName = $site->theme_type == 'demo' ? optional($site)->demo->name : optional($site)->backupSite->title;
            if ($site->status && $site->domain != null && $site->domain_type != 'free_domain') {
                $dnsInfo = (new LightsailService(auth()->user()->aws_account_id, $site->aws_region))->domainDetails($site->domain)['dns'];
                $take_time = 7;
            } else {
                $dnsInfo = 'failed';
                $take_time = 10;
            }
            $lastPaymentHistory = optional($site->subscription)->latestPaymentHistory;
            $data = [
                'success' => 'Order has been created successfully. It will take '.$take_time.' minutes to complete setup.',
                'msg' => 'Please update name servers from your domain provider company.',
                'site' => $site,
                'sourceName' => $sourceName,
                'specification' => $plan,
                'transaction_data' => $lastPaymentHistory,
                'dnsInfo' => $dnsInfo,
            ];

            session()->forget(codibu_session());

            return view('sites.thank-you', $data);
        } else {
            return redirect()->route('sites.index');
        }
    }
}
