<!DOCTYPE html>
<html lang="<?php echo e(__("head.lang"), false); ?>">

<head>
    <meta charset="UTF-8">
    <meta name="facebook-domain-verification" content="jj012vr1pahww52z2wdok5e06ubo3n" />
    <meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=0'>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="<?php echo e(csrf_token(), false); ?>">
    <title><?php echo e(config('app.name'), false); ?> - <?php echo $__env->yieldContent('page-title'); ?></title>
    <meta name="description" content="<?php echo e(__("head.description"), false); ?>">
    <meta name="author" content="Codibu.com">
    <meta name="keywords" content="<?php echo e(__("head.keywords"), false); ?>">
    <link rel="shortcut icon" type="image/x-icon" href="<?php echo e(asset('assets/images/brand/favicon.ico'), false); ?>" />
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- <link rel="stylesheet" href="<?php echo e(asset('css/app.bootstrap.5.1.css'), false); ?>"> -->
    <link rel="stylesheet" href="<?php echo e(asset('css/hover.min.css'), false); ?>">
    <?php if(\Route::current()->getName() != 'register'): ?>
        <link href="<?php echo e(URL::asset('assets/css/icons.css'), false); ?>" rel="stylesheet"/>
    <?php endif; ?>
    <?php echo $__env->yieldPushContent('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/custom.css'), false); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.1.0/magnific-popup.min.css">
    <meta name="description" content="Create a professional website with Codibu. We offer Custom Web App Development, and Ecommerce Website Design &amp; Development. To know more visit us now." />
    <?php echo $__env->make('frontend.include.header_link', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-4XK4VN2VX2"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-4XK4VN2VX2');
    </script>
</head>

<body>
<?php echo $__env->yieldContent('content'); ?>
<script>window.APP_URL = <?php echo json_encode(url('/')); ?></script>
<script src="<?php echo e(asset('js/app.bootstrap.5.1.js'), false); ?>" type="text/javascript"></script>
<script src="<?php echo e(asset('assets/js/image-scroller.js'), false); ?>"></script>
<script src="<?php echo e(asset('js/custom.js'), false); ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.1.0/jquery.magnific-popup.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        $('#language').change(function() {

            var selectedValue = $(this).val();
            var url = $(location).prop('href');

            if (selectedValue == 'kr' && url.includes('/kr')){
                window.location.href = $(location).prop('href');
            } else if (selectedValue == 'kr' && url.includes('/kr') != true){
                window.location.href = url+'/kr';
            } else if (selectedValue == 'en' && url.includes('/kr')) {
                window.location.href = url.replace('/kr','');
            } else {
                window.location.href = url;
            }
        });
    });
</script>

<?php echo $__env->make('frontend.include.footer_link', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<script>

    let base_url = "<?php echo e(config('app.url'), false); ?>";
    /*=========START LOG IN SCRIPT==========*/

    $('.sweet_confirm_modal').click(function () {
        let route = $(this).data('route');
        let title = $(this).data('title');

        Swal.fire({
            title: `<span style="font-family:sans-serif;font-size: 1.4rem; line-height:2.6rem;">Continue with ${title}?</span>`,
            html: `<span style="font-size: 1rem;line-height: 21px;font-weight: 400;">Yes, I certify that I have read and accept the<br> <a class="info" style="cursor: pointer; color:#0774f8;" onclick = "openPopUp('Privacy-Policy-Sign')" >Privacy-Policy</a>, <a class="info" style="cursor: pointer; color:#0774f8;" onclick = "openPopUp('Professional-Services-Agreement')">Professional Services Agreement</a> & <a class="info" style="cursor: pointer; color:#0774f8;" onclick = "openPopUp('User-Agreement-Sign')">User Agreement.</a></span>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Confirm',
            customClass: {
                confirmButton: 'bg-sky hover-animation',
            }
            }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = route;
            }
        })
    })

    // input text validation
    document.querySelectorAll('input[type="text"]').forEach(item => {
        item.addEventListener('input', function () {
            let inputText = item.value
            if (inputText.length > 2) {
                item.nextElementSibling.classList.add('active')
            } else {
                item.nextElementSibling.classList.remove('active')
            }
        })
    })

    // password eye show hide
    $('.pass-eye').click(function () {
        $('.pass-eye').addClass('active')
        $(this).removeClass('active')
        if ($('.eye-on').hasClass('active')) {
            $(this).siblings('input').attr('type', 'text');
        } else {
            $(this).siblings('input').attr('type', 'password')
        }
    })
    $('.pass-eye-2').click(function () {
        $('.pass-eye-2').addClass('active')
        $(this).removeClass('active')
        if ($('.eye-on').hasClass('active')) {
            $(this).siblings('input').attr('type', 'text');
        } else {
            $(this).siblings('input').attr('type', 'password')
        }
    })

    // email validation
    let emailInput = document.querySelector('input[type="email"]');
    emailInput.addEventListener('input', function () {
        let email = emailInput.value;
        if (isEmailValid(email)) {
            emailInput.nextElementSibling.classList.add('active')
        } else {
            emailInput.nextElementSibling.classList.remove('active')
        }
    })

    function isEmailValid(email) {
        const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }

    document.querySelectorAll('.control-form').forEach(item => {
        item.addEventListener('focus', function () {
            item.parentNode.classList.add('active-gradient')
        })
        item.addEventListener('blur', function () {
            item.parentNode.classList.remove('active-gradient')
        })
    })

    /*=========END LOG IN SCRIPT=========*/

    // Enhanced Login Page Interactions
    $(document).ready(function() {
        // Add loading animation to login button
        $('#loginBtn').click(function(e) {
            const btn = $(this);
            if (btn.closest('form')[0].checkValidity()) {
                btn.addClass('loading');
                btn.text('Logging in...');
            }
        });

        // Enhanced input focus effects
        $('.control-form').on('focus', function() {
            $(this).closest('.border-gradient').addClass('active-gradient');
        }).on('blur', function() {
            $(this).closest('.border-gradient').removeClass('active-gradient');
        });

        // Floating label effect
        $('.control-form').each(function() {
            const input = $(this);
            const placeholder = input.attr('placeholder');

            input.on('focus', function() {
                if (!input.val()) {
                    input.attr('placeholder', '');
                }
            }).on('blur', function() {
                if (!input.val()) {
                    input.attr('placeholder', placeholder);
                }
            });
        });

        // Language selector enhancement
        $('#filter').change(function() {
            const selectedValue = $(this).val();
            const currentUrl = window.location.href;

            if (selectedValue === 'kr' && !currentUrl.includes('/kr')) {
                window.location.href = currentUrl + '/kr';
            } else if (selectedValue === 'en' && currentUrl.includes('/kr')) {
                window.location.href = currentUrl.replace('/kr', '');
            }
        });

        // Add smooth scroll to alerts
        if ($('.alert').length) {
            $('html, body').animate({
                scrollTop: $('.alert').offset().top - 100
            }, 500);
        }

        // Enhanced password visibility toggle
        $('.pass-eye').click(function() {
            const icon = $(this);
            const input = icon.siblings('input');
            const isPassword = input.attr('type') === 'password';

            input.attr('type', isPassword ? 'text' : 'password');

            // Add visual feedback
            icon.css('transform', 'translateY(-50%) scale(1.2)');
            setTimeout(() => {
                icon.css('transform', 'translateY(-50%) scale(1)');
            }, 150);
        });

        // Form validation enhancement
        $('form').submit(function(e) {
            const form = $(this);
            let isValid = true;

            form.find('.control-form[required]').each(function() {
                const input = $(this);
                const borderGradient = input.closest('.border-gradient');

                if (!input.val().trim()) {
                    borderGradient.addClass('is-invalid-error');
                    isValid = false;
                } else {
                    borderGradient.removeClass('is-invalid-error');
                }
            });

            if (!isValid) {
                e.preventDefault();
                // Shake animation for invalid form
                form.addClass('shake');
                setTimeout(() => form.removeClass('shake'), 500);
            }
        });
    });
</script>

<script> // function to open the popup window
    function openPopUp(name) {
          let url = `${APP_URL}/${name}`;//"<?php echo e(route('Privacy-Policy-Sign'), false); ?>";
          let height = 600;
          let width = 900;
          var left = ( screen.width - width ) / 2;
          var top = ( screen.height - height ) / 2;
          var newWindow = window.open( url, "center window", 'resizable = yes, width=' + width + ', height=' + height + ', top='+ top + ', left=' + left);
       }
 </script>
<?php echo $__env->yieldPushContent('js'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\codibu\resources\views/frontend/layouts/app_login.blade.php ENDPATH**/ ?>