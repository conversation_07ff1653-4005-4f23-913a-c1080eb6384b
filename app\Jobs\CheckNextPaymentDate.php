<?php

namespace App\Jobs;

use App\Mail\SubscriptionPaymentFailed;
use App\Models\Site;
use App\Models\Subscription;
use App\Services\LightsailService;
use App\Services\PaypalService;
use App\Services\StripeService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class CheckNextPaymentDate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // this is for payment reminder
        Subscription::where(['status' => 'Active'])
            ->where('subscription_id', 'not LIKE', '%sched%')
            ->whereHasMorph('serviceable', [\App\Models\Site::class])
            ->whereNotNull('next_billing_date')
            ->whereDate('next_billing_date', '<', Carbon::now()->toDateString())
            ->orderBy('id')
            ->chunk(5, function ($subscriptions) {
                foreach ($subscriptions as $subscription) {
                    if ($subscription->platform == 'Stripe') {
                        $subscriptiondetails = (new StripeService)->getSubscription($subscription);
                        if ($subscriptiondetails['status'] != 'canceled') {
                            $this->stopInstance($subscription);
                            $last_date = (Carbon::createFromTimestamp($subscription->next_billing_date)->addDays(9))->format('jS F');
                            Mail::to($subscription->user)->queue(new SubscriptionPaymentFailed($subscription, $last_date));
                            Log::info('CheckNextPaymentDate');
                        }
                    } elseif ($subscription->platform == 'PayPal') {
                        $subscriptiondetails = (new PaypalService)->getSubscriptionDetails($subscription->subscription_id);
                        if ($subscriptiondetails['status'] != 'CANCELLED') {
                            $this->stopInstance($subscription);
                            $last_date = (Carbon::createFromTimestamp($subscription->next_billing_date)->addDays(9))->format('jS F');
                            Mail::to($subscription->user)->queue(new SubscriptionPaymentFailed($subscription, $last_date));
                            Log::info('CheckNextPaymentDate');
                        }
                    }
                }
            });
    }

    public function stopInstance(Subscription $subscription)
    {
        $site = Site::where('id', optional($subscription->serviceable)->id)->first();
        if ($site) {
            $site->status = 'terminated';
            $site->save();
            (new LightsailService($site->user->aws_account_id, $site->aws_region))->stopInstance($site);

            return redirect()->back()->with('success', 'Website Stop request has been sent.');
        } else {
            return redirect()->back()->with('success', 'Website Stop request Faild!.');
        }
        $subscription->update(['status' => 'Inactive']);
    }
}
