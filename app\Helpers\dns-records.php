<?php

if (! function_exists('dns_records')) {
    function dns_records(): array
    {
        return [
            [
                'Record_type' => 'A',
                'Record_description' => 'Routes traffic to an IPv4 address',
                'Priority' => false,
                'Weight' => false,
                'Record_name' => 'value',
                'value_lavel' => 'Resolves to',
                'placeholder' => '0.0.0.0',
                'Port' => false,
            ],
            [
                'Record_type' => 'AAAA',
                'Record_description' => 'Routes traffic to an IPv6 address',
                'Priority' => false,
                'Weight' => false,
                'Record_name' => 'value',
                'value_lavel' => 'Resolves to',
                'placeholder' => '0000:0000:0000:0000:0000:0000:0000:0000',
                'Port' => false,
            ],
            [
                'Record_type' => 'CNAME',
                'Record_description' => 'Routes traffic to another domain nameRoute traffic to',
                'Priority' => false,
                'Weight' => false,
                'Record_name' => 'value',
                'value_lavel' => 'Route traffic to',
                'placeholder' => 'blog.example.com',
                'Port' => false,
            ],
            [
                'Record_type' => 'TXT',
                'Record_description' => 'Used to verify ownership of a domain',
                'Priority' => false,
                'Weight' => false,
                'Record_name' => 'value',
                'value_lavel' => 'Responds with',
                'placeholder' => '"service_key=abc123"',
                'Port' => false,
            ],
            [
                'Record_type' => 'MX',
                'Record_description' => 'Maps a subdomain to a mail server',
                'Priority' => true,
                'Weight' => false,
                'Record_name' => 'value',
                'value_lavel' => 'Route traffic to',
                'placeholder' => 'blog.example.com',
                'Port' => false,
            ],
            [
                'Record_type' => 'NS',
                'Record_description' => 'Name servers for a DNS zone',
                'Priority' => false,
                'Weight' => false,
                'Record_name' => 'value',
                'value_lavel' => 'Route traffic to',
                'placeholder' => 'blog.example.com',
                'Port' => false,
            ],
            [
                'Record_type' => 'SRV',
                'Record_description' => 'Maps a subdomain to a service address',
                'Priority' => true,
                'Weight' => true,
                'Record_name' => 'value',
                'value_lavel' => 'Route traffic to',
                'placeholder' => 'blog.example.com',
                'Port' => true,
            ],
        ];
    }
}
