<?php

namespace App\Http\Controllers;

use App\Http\Requests\TransferDomain;
use App\Models\DomainRenewPlan;
use App\Models\PaymentPlatform;
use App\Resolver\PaymentPlatformResolver;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON>\Agent\Agent;

class TransferDomainController extends Controller
{
    /**
     * @var ;
     */
    protected $paymentPlatformResolver;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(PaymentPlatformResolver $paymentPlatformResolver)
    {
        $this->paymentPlatformResolver = $paymentPlatformResolver;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $data = [
            'is_phone' => (new Agent)->isPhone() ? 1 : 0,
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'payment_methods' => auth()->user()->payment_methods,
        ];

        return view('domains.transfer', $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(TransferDomain $request)
    {
        $request->validated();

        $domain_plan = DomainRenewPlan::where('price', session('transfer_domain_price'))->first();
        $arr = array_merge($request->all(), [
            'domain' => $request->domain,
            'paypal_plan_id' => $domain_plan->paypal_plan_id,
            'stripe_plan_id' => $domain_plan->stripe_plan_id,
            'plan' => $domain_plan->id,
            'price' => session('transfer_domain_price'),
        ]);
        session()->put($arr);
        // payment start
        if ($request->payment_platform) {
            $paymentPlatform = $request->payment_platform != 'Amazon'
                ? $this->paymentPlatformResolver->resolveServices(session('payment_platform'))
                : '';
            $paypal_url = '';
            if ($request->payment_platform == 'PayPal') {
                $paypal_url = $paymentPlatform->handleTransferDomain();
            } elseif ($request->payment_platform == 'Stripe') {
                $paymentPlatform->handleTransferDomain();
            } else {
                session()->put('plan_id', $request->input('plan'));
            }
        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }
        //payment end

        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => $request->payment_platform,
        ];

        return response()->json($data, 200);
    }

    public function priceCheck(Request $request)
    {
        $arrayOfStrings = explode('.', $request->domain_name);
        $lastItem = end($arrayOfStrings);
        $data = collect(google_domain_prices())->first(function ($singleData) use ($lastItem) {
            return $singleData[0] == '.'.$lastItem;
        });

        return $data ? $data[1] : '';
    }
}
