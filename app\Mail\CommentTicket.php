<?php

namespace App\Mail;

use App\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CommentTicket extends Mailable
{
    use Queueable, SerializesModels;

    public $subject;

    public $view;

    public $message;

    public $senderMail;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($name, $mail, Ticket $ticket, $comment)
    {
        $this->senderMail = $mail;
        $this->subject = "RE: $ticket->title (Ticket ID: $ticket->ticket_id)";
        $this->message = '<body><p>'.$comment->comment.' </p>
	    <p>Replied by : '.$name.' </p>
        <p>Title : '.$ticket->title.'</p><p>Status: '.$ticket->status.'</p>
        <p>You can view the ticket at any time at <a href='.url('support-ticket/'.$ticket->id).'>View Ticket Details</a></p></body>';

    }

    /**
     * Send Ticket information to user
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.ticket_comments')->from($this->senderMail)->subject($this->subject)->with('email_content', $this->message);
    }
}
