<?php

namespace App\Admin\Controllers;

use App\Models\RequestQuote;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class RequestQuoteController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Request Quote';

    /**
     * @var ;
     */
    protected $paymentPlatformResolver;

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new RequestQuote);

        $grid->column('id', __('Id'));
        $grid->column('preference', __('Preference'));
        $grid->column('site_look', __('Site'));
        $grid->column('budget', __('Budget'));
        $grid->column('created_at', __('Created at'));
        $grid->column('updated_at', __('Updated at'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(RequestQuote::findOrFail($id));
        $show->field('preference', __('Which way do you prefer?'));
        $show->field('site_look', __('Which Site are you looking for?'));
        $show->field('budget', __('How much is the budget range?'));
        $show->field('company', __('Company'));
        $show->field('name', __('Name'));
        $show->field('position', __('Position'));
        $show->field('company_phone', __('Company Phone'));
        $show->field('phone', __('Phone'));
        $show->field('email', __('Email'));
        $show->field('homepage', __('Homepage'));
        $show->field('reference_homepage', __('Reference'));
        $show->field('contents', __('contents'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }
}
