<?php

namespace App\Http\Controllers;

use App\Services\LightsailService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class BackUpController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {

        // dd((new LightsailService(auth()->user()->aws_account_id))->getAutoSnapshots('asd-6')['autoSnapshots'][0]);
        // $carbonTime = Carbon::parse((new LightsailService(auth()->user()->aws_account_id))->getAutoSnapshots('asd-6')['autoSnapshots'][0]['createdAt']);
        // dd();
        // $seconds = $carbonTime->second + ($carbonTime->minute * 60) + ($carbonTime->hour * 3600);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
