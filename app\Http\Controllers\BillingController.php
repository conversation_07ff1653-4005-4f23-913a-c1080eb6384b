<?php

namespace App\Http\Controllers;

use App\Models\PaymentPlatform;
use App\Models\Subscription;
use App\Repository\PaymentHistoryRepository;
use App\Resolver\PaymentPlatformResolver;
use App\Services\PaypalService;
use App\Services\StripeService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Jenssegers\Agent\Agent;

class BillingController extends Controller
{
    /**
     * @var ;
     */
    protected $paymentPlatformResolver;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(PaymentPlatformResolver $paymentPlatformResolver)
    {
        $this->paymentPlatformResolver = $paymentPlatformResolver;
    }

    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|\Illuminate\Http\Response|View
     */
    public function index()
    {
        $arr = [
            'payment_methods' => auth()->user()->payment_methods,
            'is_windows' => (new Agent)->is('Windows'),
            'subscriptions' => Subscription::where('user_id', auth()->id())->get(),
        ];

        return view('billing.index')->with($arr);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, $id)
    {
        $subscription = Subscription::find($id);

        if (session()->exists('billing_subscription_id')) {
            if ($subscription->platform == 'Stripe') {
                (new StripeService)->cancelSubscription(auth()->user()->stripe_id, $subscription->subscription_id);
            } else {
                (new PaypalService)->cancelSubscription($subscription->subscription_id, 'change');
            }

            if ((session('payment_platform') == 'PayPal' && $request->status == 'confirmed')) {
                $subscription->update([
                    'subscription_id' => session('billing_subscription_id'),
                    'status' => 'Active',
                    'platform' => session('payment_platform'),
                ]);
                (new PaymentHistoryRepository)->createPaymentHistory($subscription);
            } elseif (session('payment_platform') == 'Stripe') {
                $subscription->update([
                    'subscription_id' => session('billing_subscription_id'),
                    'status' => 'Active',
                    'platform' => session('payment_platform'),
                ]);
                (new PaymentHistoryRepository)->createPaymentHistory($subscription);
            }
        }
        session()->forget(codibu_session());
        $data = [
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'payment_methods' => auth()->user()->payment_methods,
            'subscription' => $subscription,
        ];

        return view('billing.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $subscription = Subscription::find($id);
        session()->forget(codibu_session());
        if ($subscription->platform == $request->payment_platform && $request->payment_platform == 'stripe') {
            if (strpos($subscription->subscription_id, 'sched')) {
                $schedule = (new StripeService)->getSubscriptionSchedule($subscription);
                if ($schedule->status != 'canceled') {
                    (new StripeService)->updateSubscription(auth()->user()->stripe_id, $request->subscription_id, $request->payment_method);

                    return redirect()->back();
                }
            } else {
                $subscription = (new StripeService)->getSubscription($subscription);
                if ($subscription['status'] != 'canceled') {
                    (new StripeService)->updateSubscription(auth()->user()->stripe_id, $request->subscription_id, $request->payment_method);

                    return redirect()->back();
                }
            }
        }
        $plan = $subscription->subscribable;

        $arr = array_merge($request->all(), [
            'billing_start_from' => $subscription->next_billing_date < date(now()) ? false : $subscription->next_billing_date,
            'change_billing_id' => $id,
            'paypal_plan_id' => $plan->paypal_plan_id,
            'stripe_plan_id' => $plan->stripe_plan_id,
            'service_category_name' => $subscription->subscribable_type == \App\Models\ProfessionalServicePlan::class ? $plan->category->name : null,
        ]);
        $arr = array_merge($arr);
        session()->put($arr);
        // payment start
        if ($request->payment_platform) {
            $paymentPlatform = $this->paymentPlatformResolver->resolveServices(session('payment_platform'));
            $paypal_url = '';
            if (session('payment_platform') == 'PayPal') {
                $paypal_url = $paymentPlatform->handleBillingSubscription();
            } elseif (session('payment_platform') == 'Stripe') {
                $paymentPlatform->handleBillingSubscription();
            }
        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }
        //payment end

        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => $request->payment_platform,
        ];

        return json_encode($data);
    }
}
