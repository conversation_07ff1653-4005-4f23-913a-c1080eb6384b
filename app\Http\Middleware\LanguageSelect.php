<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LanguageSelect
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (strpos($request->url(), '/kr')) {
            session()->put('locale', 'kr');
        } else {
            session()->put('locale', 'en');
        }

        if (session('locale')) {
            app()->setLocale(session('locale'));
        }

        return $next($request);
    }
}
