<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Jobs\SendContactUsMail;
use App\Models\ContactMail;
use App\Rules\RecaptchaCheck;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContactMailController extends Controller
{
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'recaptcha_token' => ['bail', 'required', new RecaptchaCheck],
            'type' => 'required',
            'name' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            return redirect('/contact#contact')->withErrors($validator);
        }
        ContactMail::create($request->all());
        dispatch(new SendContactUsMail($request->all()));

        return redirect('/contact#contact')->with('successMail', 'Your message has been sent successfully');
    }
}
