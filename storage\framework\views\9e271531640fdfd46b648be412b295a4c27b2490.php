
<?php $__env->startSection('page-title'); ?>
    Login
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <!-- Enhanced Login Section with Modern Design -->
    <div class="fancy-login-wrapper">
        <!-- Ultra Modern Animated Background -->
        <div class="animated-bg">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
                <div class="shape shape-6"></div>
            </div>
            <!-- Particle System -->
            <div class="particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>
        </div>

        <div class="login-section user-account d-flex flex-wrap">
            <div class="left-section enhanced-left">
                <a href="<?php if(session('locale') != 'kr'): ?> <?php echo e(route('landingEn'), false); ?> <?php else: ?> <?php echo e(route('landingKr'), false); ?> <?php endif; ?>" class="logo-link">
                    <img src="<?php echo e(asset('images/new-logo-white.svg'), false); ?>" alt="codibu logo" class="logo-img">
                </a>
                <div class="d-flex flex-column h-100 justify-content-center">
                    <div class="welcome-content">
                        <h1 class="welcome-title">
                            <span class="gradient-text"><?php echo e(__('login.One-Stop'), false); ?></span>
                            <br><span class="gradient-text"><?php echo e(__('login.Website'), false); ?></span>
                            <br><span class="gradient-text"><?php echo e(__('login.Solution'), false); ?></span>
                        </h1>
                        <p class="welcome-subtitle">Experience the future of web development with our cutting-edge platform designed for modern creators</p>
                        <!-- Modern Stats -->
                        <div class="stats-container">
                            <div class="stat-item">
                                <div class="stat-number">10K+</div>
                                <div class="stat-label">Active Users</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">99.9%</div>
                                <div class="stat-label">Uptime</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">24/7</div>
                                <div class="stat-label">Support</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="align-items-sm-center d-flex right-section enhanced-right">

            <div class="login-content">

                <?php if(Session::has('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <p><?php echo e(Session::get('success'), false); ?></p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php elseif(Session::has('warning')): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <p><?php echo e(Session::get('warning'), false); ?></p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php elseif(Session::has('danger')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <p><?php echo e(Session::get('danger'), false); ?></p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php elseif(Session::has('info')): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <p><?php echo e(Session::get('info'), false); ?></p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php elseif(Session::has('message')): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <p><?php echo e(Session::get('message'), false); ?></p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>


                <a href="https://codibu.com" class="logo d-block d-sm-none">
                    <img href="<?php echo e(url(''), false); ?>" src="<?php echo e(asset('images/new-logo.svg'), false); ?>" alt="codibu logo">
                </a>
                <h2> <?php echo e(__('login.login-to'), false); ?></h2>

                <div class="divider" data-text="<?php echo e(__('login.data-text1'), false); ?>"></div>
                <div class="social-login d-sm-flex d-block">
                    <?php echo $__env->make('frontend.component.social_login', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="divider" data-text=" <?php echo e(__('login.data-text2'), false); ?>"></div>

                <form method="POST" action="<?php echo e(route('login'), false); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="group-input">
                        <!-- <label for="email" class="form-label">Email address*</label> -->
                        <div class="border-gradient <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <input type="email" class="control-form" name="email" id="email"
                                   placeholder="<?php echo e(__('<EMAIL>'), false); ?>" value="<?php echo e(old('email'), false); ?>" required>
                            <img class="position-icon cursor-pointer" src="<?php echo e(asset('images/icon/check-gradient.svg'), false); ?>"
                                 alt="codibu">
                        </div>
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback-error">
                            <?php echo e($message, false); ?>

                        </div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="group-input">
                        <!-- <label for="password" class="form-label">Password*</label> -->
                        <div class="border-gradient position-relative <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <input type="password" name="password" required class="control-form" id="password"
                                   placeholder="********">
                            <img class="eye-off pass-eye position-icon active cursor-pointer"
                                 src="<?php echo e(asset('images/icon/eye-off.svg'), false); ?>" alt="codibu">
                            <img class="eye-on pass-eye position-icon cursor-pointer"
                                 src="<?php echo e(asset('images/icon/eye-on.svg'), false); ?>" alt="codibu">


                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback-error">
                                <?php echo e($message, false); ?>

                            </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                        </div>


                    </div>

                    <div class="group-input">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember"
                                <?php echo e(old('remember') ? 'checked' : '', false); ?>><?php echo e(__('login.remember-me'), false); ?>

                            <label class="form-check-label" for="remember">
                                <?php echo e(__('Remember Me'), false); ?>

                            </label>
                            <a href="<?php echo e(route('password.request'), false); ?>" class="float-end"><?php echo e(__('login.forgot'), false); ?></a>
                        </div>
                    </div>

                    <button class="hover-animation login-button" type="submit" id="loginBtn"> <?php echo e(__('login.Login'), false); ?></button>


                    <div class="divider" data-text=""></div>

                    <div class="group-input" style="text-align: center">
                        <a href="<?php if(session('locale') != 'kr'): ?> <?php echo e(url('/register'), false); ?> <?php else: ?> <?php echo e(url('/register/kr'), false); ?> <?php endif; ?>" id="signuplink"><?php echo e(__('login.signUp'), false); ?></a>
                    </div>

                </form>
                <div style="text-align: center">
                    <label for="cars" style="margin-right:10px;">Choose Language:</label>
                    <span class="outerFilterBox">
                    <select name="" id="filter">
                        <option value="en" <?php if(session('locale') == 'en'): ?> selected <?php endif; ?>>English</option>
                        <option value="kr" <?php if(session('locale') == 'kr'): ?> selected <?php endif; ?>>Korean</option>
                    </select>
                </span>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.app_login', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\codibu\resources\views/auth/login.blade.php ENDPATH**/ ?>