<?php

namespace App\Http\Controllers;

use App\Mail\SupportTicketMail;
use App\Mail\SupportTicketNotificationMailToAdmin;
use App\Models\Ticket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;

class SupportTicketController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('support_tickets.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'title' => 'required',
            'category' => 'required',
            'priority' => 'required',
            'message' => 'required',
        ]);

        $ticket = new Ticket([
            'title' => $request->input('title'),
            'user_id' => Auth::user()->id,
            'ticket_id' => strtoupper(Str::random(10)),
            'category' => $request->input('category'),
            'priority' => $request->input('priority'),
            'message' => $request->input('message'),
            'status' => 'Open',
        ]);

        $ticket->save();

        //$mailer->sendTicketInformation(Auth::user(), $ticket);
        if (Auth::user()->email) {
            Mail::to(Auth::user()->email)->queue(new SupportTicketMail(Auth::user(), $ticket));
            Mail::to('<EMAIL>')->queue(new SupportTicketNotificationMailToAdmin(Auth::user(), $ticket));
        }

        return redirect()->back()->with('scc_msg', "A ticket with ID: #$ticket->ticket_id has been opened.");
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $ticket = Ticket::where('id', $id)->first();
        //dd($ticket);
        $comments = $ticket->comments;

        return view('support_tickets.show', compact('ticket', 'comments'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    /**
     * Display all tickets by a user.
     *
     * @return \Illuminate\Http\Response
     */
    public function userTickets(Request $request)
    {
        if ($request->ajax()) {
            $page = $request->page;
            $tickets = Ticket::where('user_id', Auth::user()->id)
                ->orderBy('id', 'desc')
                ->paginate(20);
            $pagination = (string) $tickets->links();

            return json_encode(compact('tickets', 'pagination'));
        } else {
            return view('support_tickets.user_tickets');
        }

    }
}
