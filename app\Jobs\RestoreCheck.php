<?php

namespace App\Jobs;

use App\Models\Site;
use App\Services\LightsailService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RestoreCheck implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $currentDate = Carbon::now();
        $sevenDaysAgo = $currentDate->subDays(7);
        $sites = Site::where('new_instance_date', '<=', $sevenDaysAgo)->get();
        foreach ($sites as $site) {
            $site->max_restore = 1;
            $aws_account_id = $site->user->aws_account_id;
            $instance = (new LightsailService($aws_account_id, $site->aws_region))->getInstance($site->new_instance_name);
            if ($instance) {
                (new LightsailService($aws_account_id, $site->aws_region))->deleteInstance($site->instance_name);
                
                $site->instance_name = $site->new_instance_name;
                $site->new_instance_name = null;
                $site->new_instance_date = null;
                $site->save();

            }
        }
    }
}
