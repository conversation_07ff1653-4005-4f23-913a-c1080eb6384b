<?php

namespace App\Admin\Actions\Site;

use App\Models\Site;
use Encore\Admin\Actions\Response;
use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class PermanentlyDelete extends RowAction
{
    public $name = 'Permanently Delete';

    public function handle(Model $model): Response
    {
        $id = $model->id;
        $site = Site::find($id);
        $site->update(['status' => 'terminated']);
        $site->delete();

        return $this->response()->success('Success! This site is permanently deleted')->refresh();
    }

    public function form()
    {
        $this->text('answer', '5 X 6 = ?')->rules('required|regex:(30)', [
            'required' => 'Please enter the answer.',
            'regex' => 'Incorrect answer.',
        ]);
    }
}
