<?php

namespace App\Jobs;

use App\Services\LightsailService;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class InstallSite implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $site;

    private $ip;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($site, $ip)
    {
        $this->site = $site;
        $this->ip = $ip;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $user = User::find($this->site->user_id);
        Log::info('check job InstallSite.php');
        if ($this->site->theme_type == 'demo') {
            (new LightsailService($user->aws_account_id, $this->site->aws_region))->installSite($this->site, $this->ip);
        } else {
            (new LightsailService($user->aws_account_id, $this->site->aws_region))->installSiteFromBackup($this->site, $this->ip);
        }
    }
}
