<?php

namespace App\Http\Controllers;

class LanguageController extends Controller
{

    public function en()
    {
        if (strpos(url()->previous(), 'kr')) {
            session(['locale' => 'en']);

            return redirect()->route('landingEn');
        }
        session(['locale' => 'en']);

        // app()->setLocale('en');
        return redirect()->back();
    }

    public function kr()
    {
        if (url()->previous() == 'https://'.request()->getHost().'/') {
            session(['locale' => 'kr']);

            return redirect()->route('landingKr');
        }
        session(['locale' => 'kr']);

        // app()->setLocale('kr');
        return redirect()->back();
    }
}
