<?php

namespace App\Console\Commands;

use App\Models\Site;
use App\Services\LightsailService;
use App\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class InstallSSLCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'toprankon:InstallSSL';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('SSL0');
        Site::where('status', 'completed')->where('ssl_installed', '!=', 1)->get()->each(function (Site $site) {
            Log::info('SSL1');
            $user = User::find($site->user_id);
            $instance = (new LightsailService($user->aws_account_id, $site->aws_region))->getInstance($site->instance_name);
            if (isset($instance)) {
                Log::info('SSL2');
                if ($instance['instance']['state']['name'] == 'running') {
                    Log::info('SSL3');
                    \App\Jobs\InstallSSLJob::dispatchNow($site);
                }
            }
        });
    }
}
