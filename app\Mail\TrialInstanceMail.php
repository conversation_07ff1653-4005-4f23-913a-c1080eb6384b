<?php

namespace App\Mail;

use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TrialInstanceMail extends Mailable
{
    use Queueable, SerializesModels;

    public $site;

    public $subject;

    public $message;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($site)
    {
        $user = User::find($site->user_id);
        $this->subject = 'Codibu Subscription';
        $this->message = "<body>
        <p>Hello $user->name,</p>
        <p>
            We would like to express our gratitude to ".$user->name." for creating a website using the trial plan. However, it will be terminated after 7 days and deleted after 14 days. To prevent this, it is necessary to subscribe.
        </p>
        <p>
            Please follow the provided link to subscribe to your desired plan <a href='https://codibu.com/sites/{$site->id}'>https://codibu.com/sites/{$site->id}</a>
        </p>
        </body>";
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.trial_instance_mail')->from('<EMAIL>')->subject($this->subject)->with('email_content', $this->message);
    }
}
