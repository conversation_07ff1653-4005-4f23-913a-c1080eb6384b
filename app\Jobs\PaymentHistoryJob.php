<?php

namespace App\Jobs;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class PaymentHistoryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('check123');
        Subscription::where('status', 'Active')
            ->chunk(5, function ($subscriptions) {
                foreach ($subscriptions as $subscription) {
                    Log::info('check123: '.$subscription->id);
                    (new \App\Repository\PaymentHistoryRepository)->createPaymentHistory($subscription);
                }
            });
    }
}
