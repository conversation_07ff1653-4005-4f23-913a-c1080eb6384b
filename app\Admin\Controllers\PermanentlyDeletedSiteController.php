<?php

namespace App\Admin\Controllers;

use App\Models\Site;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid;

class PermanentlyDeletedSiteController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Deleted Site';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Site);
        $grid->model()->onlyTrashed();
        $grid->column('id', __('Id'));
        $grid->column('title', __('Title'));
        $grid->column('slug', __('Slug'));
        $grid->column('domain', __('Domain'));
        $grid->column('server_ip', __('IP'));
        $grid->column('status')->using(['terminated' => 'Permanently Terminated', 'completed' => 'Active', 'building' => 'Building', 'stopped' => 'stopped'])->badge('green');
        $grid->column('subscription.status', __('Subscription Status'));
        $grid->column('subscription.next_billing_date', __('Next Billing Date'));
        $grid->column('created_at', __('Created at'));
        $grid->column('updated_at', __('Updated at'));
        $grid->actions(function ($actions) {
            $actions->disableEdit();
            $actions->disableDelete();
            $actions->disableView();
        });

        return $grid;
    }
}
