<?php

namespace App\Admin\Actions\Site\Payment;

use App\Services\AmazonPayService;
use App\Services\PaypalService;
use App\Services\StripeService;
use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class PartialRefund extends RowAction
{
    public $name = 'Partial Refund';

    public function handle(Model $model, Request $request)
    {
        if ($model->platform == 'Stripe') {
            (new StripeService)->createRefund($model->charge, $request->get('amount'));
        } elseif ($model->platform == 'Amazon') {
            (new AmazonPayService)->createRefund($model->charge, $request->get('amount'));
        } else {
            (new PaypalService)->createRefund($model->charge, $request->get('amount'));
        }

        $model->refund_amount += $request->get('amount');
        $model->save();

        return $this->response()->success('Success message.')->refresh();
    }

    public function form()
    {
        $this->text('amount', 'Amount')->rules('required');
    }
}
