<?php

namespace App\Admin\Controllers;

use App\Mail\CommentTicket;
use App\Models\Comment;
use App\Models\Ticket;
use App\User;
use Encore\Admin\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class CommentsController extends AdminController
{
    /**
     * Persist comment and mail user
     *
     * @param  AppMailer  $mailer
     * @return Response
     */
    public function postComment(Request $request)
    {
        // $this->validate($request, [
        //     'comment' => 'required',
        // ]);

        $comment = Comment::create([
            'ticket_id' => $request->input('ticket_id'),
            'user_id' => Auth::user()->id,
            'is_admin' => 1,
            'comment' => $request->input('comment'),
        ]);
        $ticket = Ticket::find($request->input('ticket_id'));
        $name = Auth::user()->username;
        Mail::to($ticket->user->email)->send(new CommentTicket($name, '<EMAIL>', $comment->ticket, $comment));

        return json_encode(['status' => true, 'message' => 'Your comment has been submitted.']);
    }
}
