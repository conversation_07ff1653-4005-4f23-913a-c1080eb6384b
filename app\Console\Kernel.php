<?php

namespace App\Console;

use App\Jobs\CheckAutomaticCancelSubscriptions;
use App\Jobs\CheckDownloadLimit;
use App\Jobs\CheckNextPaymentDate;
use App\Jobs\PaymentHistoryJob;
use App\Jobs\RestoreCheck;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     * /opt/cpanel/ea-php73/root/usr/bin/php
     *
     * @var array
     */
    protected $commands = [
        Commands\DownloadUpdates::class,
        Commands\CheckUpdates::class,
        Commands\ProcessThemes::class,
        Commands\ImportDemos::class,
        Commands\DownloadNetworkPlugins::class,
        Commands\InstallSite::class,
        Commands\SetAwsOrgAccountIdInUserModel::class,
        Commands\DebugOn::class,
        Commands\DebugOff::class,
        Commands\InstallSSLCommand::class,
        Commands\DomainRenew::class,
        Commands\RemoveUnverifiedUser::class,
        Commands\CheckTrialPeriod::class,
        Commands\CheckSwap::class,
        Commands\SetSwap::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('toprankon:check')->weekly();
        $schedule->command('network:download')->monthly();
        $schedule->command('toprankon:download')->monthly();
        $schedule->command('toprankon:CheckTrialPeriod')->hourly();
        $schedule->command('toprankon:removeUnverifiedUser')->hourly();
        $schedule->command('toprankon:domainAutoRenew')->everySixHours();
        $schedule->command('toprankon:installsite')->everyMinute();
        $schedule->command('toprankon:addiamkeypair')->everyMinute();
        $schedule->command('toprankon:InstallSSL')->everyThreeMinutes();
        $schedule->job(new CheckDownloadLimit)->daily();
        $schedule->job(new RestoreCheck)->daily();
        $schedule->job(new CheckNextPaymentDate)->weekly()->days([1, 4]);
        $schedule->job(new CheckAutomaticCancelSubscriptions)->daily();
        $schedule->job(new PaymentHistoryJob)->everyTenMinutes();

        //        $schedule->job(new GenerateSiteScore())->everyMinute();
        //$schedule->job(new SiteRenewalNotifyJob())->daily();

        //
        //$schedule->command('toprankon:update_info')->daily();*/
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        // $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
