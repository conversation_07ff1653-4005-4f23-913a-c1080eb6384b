<?php

namespace App\Console\Commands;

use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class RemoveUnverifiedUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'toprankon:removeUnverifiedUser';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $users = User::where('email_verified_at', null)->get();
        foreach ($users as $user) {
            $from = Carbon::createFromFormat('Y-m-d H:s:i', $user->created_at);
            $to = Carbon::createFromFormat('Y-m-d H:s:i', Carbon::now());
            $diff_in_hours = $from->diffInHours($to);
            if ($diff_in_hours >= 48) {
                $user->delete();
            }
        }
    }
}
