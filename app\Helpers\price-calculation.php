<?php

use App\Models\Coupon;
use App\Models\Plan;
use App\Models\ProfessionalServicePlan;
use Carbon\Carbon;

if (! function_exists('priceCalculation')) {

    function priceCalculation($request)
    {
        if ($request['type'] == 'site') {
            $plan = Plan::find($request['plan_id']);
            $price = $plan->discount_percentage > 0
                ? (float) $plan->price_after_discount
                : (float) $plan->price;
        } else {
            $plan = ProfessionalServicePlan::find($request['plan_id']);
            $price = (float) $plan->price;
        }

        $discount = 0;
        $status = false;
        $coupon_id = null;
        $coupon = Coupon::firstWhere('coupon_code', $request['coupon_code']);

        if (empty($coupon)) {
            $msg = 'Code is not valid!';

        } elseif (Carbon::now()->toDateString() > $coupon->expire_date) {
            $msg = 'Code is expired!';

        } elseif ($coupon->check_availability != 0) {
            if ($coupon->coupon_availability == 4) {
                $msg = 'Coupon code can be redeemed once per month.';
            } else {
                $msg = 'Code already used!';
            }
        } else {
            $coupon_id = $coupon->id;
            $calculatedPrice = calculateDiscountAmount($coupon->discount, $coupon->condition_discount, $price);
            $price = $calculatedPrice['total_price'];
            $discount = $calculatedPrice['discount'];
            $msg = 'Code applied';
            $status = true;
        }

        return [
            'coupon_status' => $status,
            'msg' => $msg,
            'plan_name' => $plan->name,
            'plan_price' => $plan->price,
            'plan_duration' => $plan->duration,
            'plan_duration_count' => $plan->duration_count,
            'total_price' => $price,
            'coupon_discount' => $discount,
            'coupon_id' => $coupon_id,
            'type' => $request['type'],
        ];
    }
}

if (! function_exists('calculateDiscountAmount')) {

    function calculateDiscountAmount($discount, $condition, $price)
    {
        if ($condition == 1) {
            $discount = (($price * $discount) / 100);
            $grand_price = $price - number_format($discount, 2);

        } else {
            $grand_price = $price - $discount;
        }

        return [
            'total_price' => number_format($grand_price, 2, '.', ''),
            'discount' => number_format($discount, 2, '.', ''),
        ];
    }
}
