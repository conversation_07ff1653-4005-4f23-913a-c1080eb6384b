<?php

namespace App\Http\Controllers;

use App\Models\PaymentPlatform;
use App\Models\ProfessionalServicePlan;
use App\Resolver\PaymentPlatformResolver;
use Illuminate\Http\Request;
use Jenssegers\Agent\Agent;

class CustomInvoiceController extends Controller
{
    /**
     * @var ;
     */
    protected $paymentPlatformResolver;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(PaymentPlatformResolver $paymentPlatformResolver)
    {
        $this->paymentPlatformResolver = $paymentPlatformResolver;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($custom_invoice)
    {
        $data = [
            'professionalService' => ProfessionalServicePlan::where('custom_invoice', $custom_invoice)->first(),
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'payment_methods' => auth()->user()->payment_methods,
            'is_windows' => (new Agent)->is('Windows'),
        ];

        return view('custom_invoice.create', $data);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $custom_invoice)
    {
        session()->forget(codibu_session());

        $plan = ProfessionalServicePlan::where('custom_invoice', $custom_invoice)->first();
        $arr = array_merge($request->all(), [
            'paypal_service_plan_id' => $plan->paypal_plan_id,
            'stripe_service_plan_id' => $plan->stripe_plan_id,
            'service_category_name' => $plan->category->name,
        ]);
        $data = [
            'plan' => $plan->id,
            'plan_name' => $plan->name,
            'plan_price' => $plan->price,
            'plan_duration' => $plan->duration,
            'plan_duration_count' => $plan->duration_count,
            'total_price' => $plan->price,
        ];
        $arr = array_merge($arr, $data);

        session()->put($arr);

        // payment start
        if ($request->payment_platform) {
            $paymentPlatform = $this->paymentPlatformResolver->resolveServices(session('payment_platform'));
            $paypal_url = '';
            if ($plan->duration != 'one-time') {
                $paypal_url = $paymentPlatform->handleProfessionalServiceSubscription();
            } else {
                $paypal_url = $paymentPlatform->createOneTimePayment();
            }
        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }
        //payment end

        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => $request->payment_platform,
        ];

        return json_encode($data);
    }
}
