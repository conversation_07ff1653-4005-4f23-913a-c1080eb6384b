<?php

namespace App\Http\Controllers;
use App\Models\Subscription;
use App\Services\StripeService;

class TestController extends Controller
{
    public function __invoke()
    {
         // this is for payment reminder
        $subscription = Subscription::where(['subscription_id' => 'sub_1QKVHdEJ9tCg66gFkV1kHIBS'])->first();
        $subscriptiondetails = (new StripeService)->getSubscription($subscription);
        dd($subscriptiondetails);
    }
}

