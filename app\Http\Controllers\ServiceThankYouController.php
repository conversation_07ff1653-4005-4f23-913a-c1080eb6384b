<?php

namespace App\Http\Controllers;

use App\Mail\UserWithProfessionalService;
use App\Models\Subscription;
use App\Repository\ProfessionalServiceRepository;
use App\Repository\SubscriptionRepository;
use App\Services\PaypalService;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ServiceThankYouController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        //return view('professional_service.thank-you');
        if(session()->has('Pre_Subscription_id')){
            //dd(session('Pre_Subscription_id'));
            $subscription = Subscription::find(session('Pre_Subscription_id'));
            if($subscription->platform == 'PayPal'){
                (new PaypalService)->cancelProfessionalServiceSubscription($subscription->subscription_id);
            } else {
                (new StripeService)->cancelProfessionalServiceSubscription($subscription->subscription_id);
            }
            $service = (new ProfessionalServiceRepository)->updateProfessionalService($subscription->serviceable_id);
            //dd($service);
            (new SubscriptionRepository)->createSubscription('professional_service', $service->id);

            $subscription = Subscription::where('subscription_id', session('service_subscription_id'))->first();

            Mail::to(auth()->user())->cc(['<EMAIL>'])->queue(new UserWithProfessionalService($subscription, auth()->user()));
            session()->forget(codibu_session());

            return view('professional_service.thank-you')->with('subscription', $subscription);
        } elseif (session()->has('service_subscription_id')) {
            $service = (new ProfessionalServiceRepository)->createProfessionalService();

            (new SubscriptionRepository)->createSubscription('professional_service', $service->id);

            $subscription = Subscription::where('subscription_id', session('service_subscription_id'))->first();

            Mail::to(auth()->user())->cc(['<EMAIL>'])->queue(new UserWithProfessionalService($subscription, auth()->user()));
            session()->forget(codibu_session());

            return view('professional_service.thank-you')->with('subscription', $subscription);
        } elseif (session()->exists('order_id')) {

            if (session('payment_platform') == 'PayPal') {
                (new PaypalService)->captureOneTimePayment();
            }

            $service = (new ProfessionalServiceRepository)->createProfessionalService();

            $subscription = (new SubscriptionRepository)->createSubscription('professional_service', $service->id);

            Mail::to(auth()->user())->queue(new UserWithProfessionalService($subscription, auth()->user()));
            session()->forget(codibu_session());

            return view('professional_service.thank-you')->with('subscription', $subscription);

        } else {
            return redirect(url('/professional-services'));
        }
    }
}
