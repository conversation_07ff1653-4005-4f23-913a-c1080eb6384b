<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class RequestQuote extends Mailable
{
    use Queueable, SerializesModels;

    public $quoteId;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->quoteId = $id;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $quote = \App\Models\RequestQuote::find($this->quoteId);

        return $this->subject('Request Quote To Expert')
            ->view('emails.request_quote', compact('quote'));

    }
}
