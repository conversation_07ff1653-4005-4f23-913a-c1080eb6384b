<?php

namespace App\Jobs;

use App\Mail\TrialInstanceMail;
use App\Models\Site;
use App\Repository\SubscriptionRepository;
use App\Services\AwsService;
use App\Services\LightsailService;
use App\Services\Route53DomainsService;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SetAwsOrgAccountIdInUserModelJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $account_id;

    private $user_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($account_id, $user_id)
    {
        $this->account_id = $account_id;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('registration');
        Log::info($this->account_id);
        (new AwsService)->importKeyPair($this->account_id);

        sleep(30);
        $user = User::where('id', $this->user_id)->first();
        $user->update(['aws_account_id' => $this->account_id]);

        Site::where(['status' => null, 'user_id' => $this->user_id])->get()->each(function (Site $site) use ($user) {

            if ($site->theme_type == 'demo') {
                $private_key_name = 'PrivateKey_'.$site->id;
                $KeyPair = (new LightsailService($user->aws_account_id, $site->aws_region))->createKeyPair($private_key_name);
                $private_key = $KeyPair['privateKeyBase64'] ? $KeyPair['privateKeyBase64'] : '';
            } else {
                $parent = Site::find($site->backup_site_id);
                $private_key_name = $parent->private_key_name;
                $private_key = $parent->private_key;
            }

            if (session('plan_price') > 1) {
                (new SubscriptionRepository)->createSubscription('site', $site->id);
            } else {
                $user->update(['free_trial' => 1]);
                Mail::to($user->email)->send(new TrialInstanceMail($site));
            }
            $staticIpName = 'staticIp-'.$site->id;
            (new LightsailService($user->aws_account_id, $site->aws_region))->createStaticIp($staticIpName);
            if ($site->domain && $site->domain_type != 'free_domain') {
                (new LightsailService($user->aws_account_id, $site->aws_region))->createDomain($site->domain);
            }

            $instanceName = Str::slug($site->title.' '.$site->id, '-');
            $site->update([
                'instance_name' => $instanceName,
                'status' => 'queued',
                'static_ip_name' => $staticIpName,
                'private_key_name' => $private_key_name,
                'private_key' => $private_key,
            ]);

            if ($site->theme_type == 'demo') {
                (new LightsailService($user->aws_account_id, $site->aws_region))->createInstance($site);
            } else {
                (new LightsailService($user->aws_account_id, $site->aws_region))->createInstancesFromSnapshot($site);
            }

            $staticIp = (new LightsailService($user->aws_account_id, $site->aws_region))->getStaticIp($site->static_ip_name);

            if ($site->domain && $site->domain_type != 'free_domain') {
                (new LightsailService($user->aws_account_id, $site->aws_region))->addDomainResource($site->domain, $staticIp['staticIp']['ipAddress']);
            }

            if ($site->domain_type == 'purchase_domain') {
                $dns_zones = (new LightsailService($user->aws_account_id, $site->aws_region))->domainDetails($site->domain)['dns'];
                $site->update(['dns_list' => json_encode($dns_zones)]);
                (new Route53DomainsService)->registrationDomain($site);
            }
        });

    }
}
