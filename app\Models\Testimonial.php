<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Testimonial extends Model
{
    protected static function booted()
    {

        static::created(function ($site) {
            Cache::forget('testimonials');
            Cache::rememberForever('testimonials', function () {
                return Testimonial::inRandomOrder()->get();
            });
        });

        static::updated(function ($site) {
            Cache::forget('testimonials');
            Cache::rememberForever('testimonials', function () {
                return Testimonial::inRandomOrder()->get();
            });
        });
    }
}
