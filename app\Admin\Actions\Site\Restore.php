<?php

namespace App\Admin\Actions\Site;

use App\Models\Site;
use App\Services\LightsailService;
use Encore\Admin\Actions\Response;
use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class Restore extends RowAction
{
    public $name = 'Restore';

    public function handle(Model $model): Response
    {
        $id = $model->id;
        $site = Site::find($id);

        if ($site) {
            $site->status = 'starting';
            $site->save();
            (new LightsailService($site->user->aws_account_id, $site->aws_region))->startInstance($site);

            return $this->response()->success('Website Start request has been sent.')->refresh();
        }

        return $this->response()->success('Success! This site is Restored')->refresh();
    }

    public function form()
    {
        $this->text('answer', '5 X 6 = ?')->rules('required|regex:(30)', [
            'required' => 'Please enter the answer.',
            'regex' => 'Incorrect answer.',
        ]);
    }
}
