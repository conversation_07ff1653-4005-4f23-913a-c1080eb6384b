<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\RequestQuote;
use App\Rules\RecaptchaCheck;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;

class PricingController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @return Application|Factory|Response|View
     */
    public function PricingQuote(Request $request): View
    {
        return view('frontend.pages.pricing');
    }

    public function PricingQuotePost(Request $request)
    {
        session()->flash('quoteForm', true);

        $this->validate($request, [
            'recaptcha_token' => ['bail', 'required', new RecaptchaCheck],
            'preference' => 'bail|required',
            'site_look' => 'bail|required',
            'budget' => 'bail|required',
            'company' => 'bail|required',
            'name' => 'bail|required',
            'position' => 'bail|required',
            'company_phone' => 'bail|required',
            'phone' => 'bail|required',
            'email' => 'bail|required',
            'contents' => 'bail|required',
        ]);
        try {
            $RequestQuote = RequestQuote::create($request->all());

            Mail::to('<EMAIL>')->queue(new \App\Mail\RequestQuote($RequestQuote->id));
        } catch (\Exception $exception) {
            Log::error('created mail error');
        }
        $success = 'Thank you for your inquiry. We appreciate your patience as we prepare our response, which may take up to 24 hours.';
        session()->flash('success', $success);

        return redirect()->route('pricing');
    }
}
