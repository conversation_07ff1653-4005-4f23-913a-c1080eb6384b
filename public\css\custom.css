* {
    font-family: 'Inter', '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== FANCY LOGIN PAGE STYLES ===== */
.fancy-login-wrapper {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Animated Background */
.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 10%;
    right: 20%;
    animation-delay: 1s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 50%;
    left: 50%;
    animation-delay: 3s;
    transform: translate(-50%, -50%);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* Login Section */
.login-section {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.enhanced-left {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.enhanced-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    z-index: -1;
}

.logo-link {
    position: absolute;
    top: 40px;
    left: 40px;
    z-index: 10;
}

.logo-img {
    max-width: 150px;
    height: auto;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.welcome-content {
    text-align: left;
    color: white;
}

.welcome-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.gradient-text {
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.welcome-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.enhanced-right {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 60px 50px;
    position: relative;
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
}

.enhanced-right::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    z-index: -1;
}

.login-content {
    max-width: 400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.login-content h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
}

.login-content h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}
/* Form Styling */
.group-input {
    margin-bottom: 25px;
    position: relative;
}

.border-gradient {
    position: relative;
    border-radius: 12px;
    padding: 2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    background-size: 300% 300%;
    animation: gradientShift 3s ease infinite;
    transition: all 0.3s ease;
}

.border-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.border-gradient.active-gradient {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.4);
}

.border-gradient.is-invalid-error {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.control-form {
    width: 100%;
    padding: 18px 20px;
    border: none;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    color: #2d3748;
    outline: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.control-form::placeholder {
    color: #a0aec0;
    font-weight: 400;
}

.control-form:focus {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.02);
}

.position-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    opacity: 0;
    transition: all 0.3s ease;
}

.position-icon.active {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

.pass-eye {
    cursor: pointer;
    z-index: 10;
}

.pass-eye:hover {
    transform: translateY(-50%) scale(1.2);
}

/* Login Button */
.login-button {
    width: 100%;
    padding: 18px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.login-button:hover::before {
    left: 100%;
}

.login-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    background: linear-gradient(45deg, #764ba2, #667eea);
}

.login-button:active {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* Divider Styling */
.divider {
    position: relative;
    text-align: center;
    margin: 30px 0;
    color: #718096;
    font-weight: 500;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}

.divider::after {
    content: attr(data-text);
    background: rgba(255, 255, 255, 0.9);
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

/* Social Login */
.social-login {
    gap: 15px;
    margin-bottom: 20px;
}

.social-login .btn {
    flex: 1;
    padding: 12px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.social-login .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Checkbox Styling */
.form-check {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 20px 0;
}

.form-check-input {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    accent-color: #667eea;
}

.form-check-label {
    color: #4a5568;
    font-weight: 500;
    cursor: pointer;
}

.float-end {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.float-end:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Language Selector */
.outerFilterBox select {
    padding: 8px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #2d3748;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.outerFilterBox select:hover {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.outerFilterBox select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Alert Styling */
.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 25px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(45deg, #48bb78, #38a169);
    color: white;
}

.alert-warning {
    background: linear-gradient(45deg, #ed8936, #dd6b20);
    color: white;
}

.alert-danger {
    background: linear-gradient(45deg, #f56565, #e53e3e);
    color: white;
}

.alert-info {
    background: linear-gradient(45deg, #4299e1, #3182ce);
    color: white;
}

.invalid-feedback-error {
    color: #e53e3e;
    font-size: 14px;
    margin-top: 8px;
    font-weight: 500;
}

.content_section
{   height: 600px;
    background: rgb(158,209,243);
    background: linear-gradient(90deg, rgba(158,209,243,1) 17%, rgba(204,225,237,1) 100%);
}

.header_section .navbar .container-fluid
{
    max-width: 1690px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .fancy-login-wrapper {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .login-section {
        flex-direction: column;
        min-height: 100vh;
    }

    .enhanced-left {
        padding: 40px 20px;
        text-align: center;
        min-height: 40vh;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .logo-link {
        position: relative;
        top: auto;
        left: auto;
        margin-bottom: 20px;
        display: block;
    }

    .welcome-title {
        font-size: 2.5rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }

    .enhanced-right {
        padding: 40px 20px;
        min-height: 60vh;
        box-shadow: none;
    }

    .login-content {
        max-width: 100%;
    }

    .login-content h2 {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .social-login {
        flex-direction: column;
        gap: 10px;
    }

    .form-check {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .shape {
        display: none;
    }
}

@media (max-width: 480px) {
    .enhanced-left {
        padding: 30px 15px;
        min-height: 35vh;
    }

    .enhanced-right {
        padding: 30px 15px;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .login-content h2 {
        font-size: 1.8rem;
    }

    .control-form {
        padding: 15px 18px;
        font-size: 15px;
    }

    .login-button {
        padding: 15px;
        font-size: 16px;
    }
}

/* Hover Effects for Desktop */
@media (min-width: 769px) {
    .enhanced-left:hover {
        background: rgba(0, 0, 0, 0.4);
    }

    .enhanced-right:hover {
        background: rgba(255, 255, 255, 1);
        box-shadow: -15px 0 40px rgba(0, 0, 0, 0.15);
    }

    .login-content:hover h2::after {
        width: 80px;
        transition: width 0.3s ease;
    }
}

/* Loading Animation */
.login-button.loading {
    pointer-events: none;
    opacity: 0.8;
}

.login-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Focus States */
.control-form:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.login-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Smooth Transitions */
* {
    transition: all 0.3s ease;
}

/* Shake Animation for Form Validation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* Signup Link Styling */
#signuplink {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 12px 24px;
    border: 2px solid #667eea;
    border-radius: 10px;
    display: inline-block;
    transition: all 0.3s ease;
    background: transparent;
}

#signuplink:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* Enhanced Mobile Logo */
@media (max-width: 575.98px) {
    .user-account .right-section .divider {
        margin: 30px 5px 20px;
    }

    #signuplink {
        margin: 10px auto;
        display: block;
        text-align: center;
        max-width: 200px;
    }

    .logo.d-block.d-sm-none img {
        max-width: 120px;
        margin: 0 auto 20px;
        display: block;
    }
}

/* Glassmorphism Effect Enhancement */
.enhanced-right {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.enhanced-left {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Improved Button Hover States */
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@-webkit-keyframes slide-bottom {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
    100% {
        -webkit-transform: translateY(100px);
        transform: translateY(100px);
    }
}
@keyframes slide-bottom {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
    100% {
        -webkit-transform: translateY(100px);
        transform: translateY(100px);
    }
}
@-webkit-keyframes slide-left {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
    100% {
        -webkit-transform: translateX(-100px);
        transform: translateX(-100px);
    }
}
@keyframes slide-left {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
    100% {
        -webkit-transform: translateX(-100px);
        transform: translateX(-100px);
    }
}
.slide-left {
    -webkit-animation: slide-left 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
    animation: slide-left 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
.slide-bottom {
    -webkit-animation: slide-bottom 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
    animation: slide-bottom 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
.navbar
{
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition:all .5s ease-in-out;
    padding: 0px;
}
.navbar-dark
{
    background:rgba(0,0,0,.8) !important;
}
.slideOut {
    top:-100px;
    -webkit-transition: all 1s ease-in-out;
    -moz-transition: all 1s ease-in-out;
    -o-transition: all 1s ease-in-out;
    transition:all 1s ease-in-out;
    padding: 0px;
    -webkit-font-smoothing: antialiased;
}
.slideIn {
    top:0px;
    -webkit-transition: all 1s ease-in-out;
    -moz-transition: all 1s ease-in-out;
    -o-transition: all 1s ease-in-out;
    transition:all 1s ease-in-out;
    padding: 0px;
    -webkit-font-smoothing: antialiased;
}

.navbar-brand
{
    width: auto;
    margin: 0;
    padding: 0;
}


.dropdown-menu{
    background-color: #000000d6;
}

.dropdown-menu.login_dropdown{
    background-color: #fff;
    min-width:340px;
    left: -360%;
    top: -2px;
    border-radius: 5px
}
.dropdown-menu.login_dropdown .modal_login_title
{
    font-size: 26px;
    line-height: 38px;
    font-weight: 500;
    letter-spacing: 0rem;
}
.dropdown-menu.login_dropdown .modal_login_title~.btn-close
{
    color: rgba(0,0,0,.3);
}

.language-dropdown-menu {
    background-color: transparent;
    padding: 0px;
    border: 0px;
    min-width: 2rem;
    margin: 0;
    font-size: 0rem !important;
}
.language-dropdown-menu .dropdown-item {
    padding: 0px 20px !important;
}

.language-dropdown img {
    height: 14px;
    width: 18px;
}
#dropdownMenuButton {
    margin: 15px;
}

.login_dropdown_modal_body form{
    /*  position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 25rem;
      padding: 2.5rem;*/
    /*  box-sizing: border-box;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 0.625rem;*/
}



.login_dropdown_modal_body .inputBox {
    position: relative;
    height:58px;
    border: 2px solid #5e835e;
    border-radius: 8px;
    margin-bottom: 10px;
}
.login_dropdown_modal_body .inputBox span
{
    background: transparent;
    border: none;
}
.login_dropdown_modal_body .inputBox input {
    width:auto;
    padding: 0.625rem 10px;
    font-size: 1rem;
    color: #000;
    letter-spacing: 0rem.062rem;
    /*  margin-bottom: 1.875rem;*/
    border: none;
    /*  border-bottom: 0.065rem solid #fff;*/
    outline: none;
    background: transparent;
}

.login_dropdown_modal_body .inputBox label {
    position: absolute;
    top: 0;
    left: 15%;
    padding: 0.9rem 0;
    font-size: 1rem;
    color: #000;
    pointer-events: none;
    transition: 0.5s;
}

.login_dropdown_modal_body .inputBox input:focus ~ label,
.login_dropdown_modal_body .inputBox input:valid ~ label,
.login_dropdown_modal_body .inputBox input:not([value=""]) ~ label {
    top: -15px;
    left: 15%;
    opacity: .7;
    font-size: 80%;
}

.login_dropdown_modal_body input[type="submit"] {
    border: none;
    outline: none;
    color: #fff;
    background-color: #03a9f4;
    padding: 0.625rem 1.25rem;
    cursor: pointer;
    border-radius: 0.312rem;
    font-size: 1rem;
}

.login_dropdown_modal_body input[type="submit"]:hover {
    background-color: #1cb1f5;
}

.login_dropdown_modal_body .submit_btn_modal{
    color: #ffffff;
    background-color: #252525;
    border-color: #252525;
    box-shadow: unset;
    font-family: sans-serif;
    font-size: 1rem;
    line-height: 16px;
    font-weight: 600;
    letter-spacing: 0rem;
    padding: 10px 30px 10px 30px;
    border-width: 3px;
    border-radius: 5px;
    width: 100%;
    overflow: hidden;
    border-style: solid;
    box-sizing: border-box;
    transition: color .1s ease-in-out,background-color .1s ease-in-out,border-color .1s ease-in-out;
}
.login_dropdown_modal_body .submit_btn_modal:hover{
    color: #252525;
    background-color: #ffffff;
    border-color: #252525;
}
.login_dropdown_modal_body .forgot_pass_txt
{
    margin: 2px 0px;
    font-size: 1rem;
    font: revert;
    vertical-align: baseline;
}
.login_dropdown_modal_body .forgot_pass_txt a
{
    text-decoration: none;
    color: #********;
    line-height: inherit;
    font-size: 1rem;
}
.login_dropdown_modal_body .forgot_pass_txt a:hover,.login_dropdown_modal_body .create_account_txt a:hover
{
    color: #000000;
}
.login_dropdown_modal_body .create_account_txt a
{
    color:#********;
    font-weight: 700;
    font-size: 1rem;
    text-decoration: none;
}

#dropdown_login_link::after
{
    border: none;
}
.dropdown-item
{
    color: #ffffff;
}
.path{
    fill:none;
    stroke:#000;
    stroke-width:1.5px;
}
.navbar-dark.bg-dark .path
{
    stroke:#fff;
}
.dropdown-menu .dropdown-item
{
    padding: 10px 20px;
    display: block;
    border-bottom: 1px solid rgba(0,0,0,.05);
}
.dropdown-menu .dropdown-item:hover{
    color: #0000FF;
    background-color: transparent;
}
.action_button
{
    display: flex;
    align-items: center;
    white-space: nowrap;
    flex-shrink: 0;
    /*color: #3c8f69;*/
    /*background-color: transparent;*/
    color: #ffffff;
    background-color: #252525;
    border-color: transparent;
    box-shadow: unset;
    font-family: sans-serif;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 0rem;
    border-width: 3px;
    border-radius: 5px;
    border-style: solid;
    padding: 12px 20px!important;
    line-height: 1.4rem!important;
    top: 0;
    text-decoration: none;
    padding: 5px 0px;
}
.action_button:hover{
    color: #252525;
    background-color: #ffffff;
}
.top-bar-right-button-dark
{
    color: #252525;
    background-color: #ffffff;
}
.navbar .nav-item .nav-link
{
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 0rem;
    padding: 5px 20px;
    line-height: 50px;
    color: #000;

}
.navbar .nav-item .nav-link:hover
{
    color: #0000FF;
}
.navbar-dark .navbar-nav .nav-link
{
    color:#ffffff !important;
}
#navbar-content .navbar-nav li:last-child
{
    margin-right: 9px;
}
#login_li
{
    text-align: center;
    vertical-align: middle;
    display: flex;
}
.dropdown-menu.show {
    -webkit-animation: fadeIn 0.3s alternate;
    /* Safari 4.0 - 8.0 */
    animation: fadeIn 0.3s alternate;
}

.nav-item.dropdown.dropdown-mega {
    position: static;
}
.nav-item.dropdown.dropdown-mega .dropdown-menu {
    width: 90%;
    top: auto;
    left: 5%;
}

.navbar-toggler {
    border: none;
    padding: 0;
    outline: none;
}
.navbar-toggler:focus {
    box-shadow: none;
}
.navbar-toggler .hamburger-toggle {
    position: relative;
    width: 50px;
    height: 50px;
    z-index: 9999;
    float: right;
    right: 4px;
    opacity: 0.6;
}
.navbar-toggler:not(.collapsed) .hamburger-toggle
{
    opacity: 1;
}
.navbar-toggler .hamburger-toggle .hamburger {
    position: absolute;
    transform: translate(-50%, -50%) rotate(0deg);
    left: 50%;
    top: 50%;
    width: 50%;
    height: 50%;
    pointer-events: none;
    color: #000000;
    background: #ffffff;
    width: 34px;
    height: 34px;
    border-radius: 3px;
}
.navbar-toggler .hamburger-toggle .hamburger.active
{
    background: transparent;
    color: #000000;
}
.navbar-toggler .hamburger-toggle .hamburger span {
    width: 70%;
    margin: 0 auto;
    height: 1.5px;
    position: absolute;
    background: #333;
    border-radius: 2px;
    z-index: 1;
    transition: transform 0.2s cubic-bezier(0.77, 0.2, 0.05, 1), background 0.2s cubic-bezier(0.77, 0.2, 0.05, 1), all 0.2s ease-in-out;
    left: 0px;
}

.navbar-toggler .hamburger-toggle .hamburger span:first-child {
    top: 28%;
    left: 5px;
    transform-origin: 50% 50%;
    transform: translate(0% -50%) !important;
}
.navbar-toggler .hamburger-toggle .hamburger span:nth-child(2) {
    top: 50%;
    left: 5px;
    transform: translate(0, -50%);
}
.navbar-toggler .hamburger-toggle .hamburger span:last-child {
    left: 5px;
    top: auto;
    bottom: 24%;
    transform-origin: 50% 50%;
}
.navbar-toggler .hamburger-toggle .hamburger.active span {
    position: absolute;
    margin: 0;
}
.navbar-toggler .hamburger-toggle .hamburger.active span:first-child {
    top: 45%;
    transform: rotate(45deg);
}
.navbar-toggler .hamburger-toggle .hamburger.active span:nth-child(2) {
    left: 50%;
    width: 0px;
}
.navbar-toggler .hamburger-toggle .hamburger.active span:last-child {
    top: 45%;
    transform: rotate(-45deg);
}

.icons {
    display: inline-flex;
    margin-left: auto;
}
.icons a {
    transition: all 0.2s ease-in-out;
    padding: 0.2rem 0.4rem;
    color: #ccc !important;
    text-decoration: none;
}
.icons a:hover {
    color: white;
    text-shadow: 0 0 30px white;
}

/*Footer Starts*/


footer
{
    padding: 50px 0 0;
    background-image: url(https://codibu.com/project/wp-content/uploads/2022/09/footer-bg.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: #ffffff;
    position: relative;
    z-index: 1;
    font-family: sans-serif;
}
footer:after
{
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #000000;
    opacity: 0.8;
    z-index: -1;
}


footer .footer_wrapper
{
    /*    padding: 0 60px 60px;*/
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}
footer .footer_img
{
    margin-bottom: 22px;
}

footer h5
{
    font-size: 1rem;
    line-height: 1.4rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
}
footer .menu_div ul
{
    padding: 0px;
    list-style-type: none;
}
footer .menu_div ul li a
{
    text-decoration: none;
    pointer-events: auto;
    color: #ffffff;
    transition: all 0.5s;
    line-height: 1.4rem;
    font-weight: 400;
    margin-bottom: 0px;
}
footer .menu_div ul li:hover a
{
    text-decoration: underline;
}
footer .about_des
{
    line-height: 1.4rem;
    font-weight: 400;
    font-size: 1.05rem;
    max-width: 560px;
    color: #fff;
}
footer .copyright_div
{
    padding: 30px 0px;
    min-height: 33px;
}
footer .copyright_div .copyright a
{
    text-decoration: none;
    color: #fff;
}
/*Footer End*/


/*Responsive Starts*/
@media screen and (min-width: 992px) {
    .navbar-nav li:hover > ul.dropdown-menu
    {
        display: block;
    }
    #login_li .nav-link
    {
        margin: 0;
        padding: 0;
        line-height: 55px;
        text-align: center;
        display: inline-table;
    }


}
@media all and (max-width: 992px) {
    footer .menu_div
    {
        padding: 0;
    }
    .icon_menu .navbar-nav {
        display: -webkit-inline-box;
        margin-right: 40px;
    }
    .action_btn_div
    {
        margin-top: 6px !important;
    }
    .dropdown-menu.login_dropdown{
        z-index: 99999;
    }
}
/* work on less than 768px */
@media all and (max-width: 768px) {
    .header_section .navbar
    {
        height: 60px;
    }
    .navbar .container-fluid
    {
        height: inherit;
        display: inline-flex;
        flex-wrap: initial;
        margin-left: 3px;
    }
    .navbar .icon_menu
    {
        height: inherit;
    }
    .navbar-brand
    {
        height: 50px!important;
        line-height: 50px!important;
        margin: 5px 0;
    }
    .navbar-brand img
    {
        width: 100px;
        height: fit-content;
    }
    .navbar .nav-item .nav-link
    {

    }
    .action_btn_div
    {
        display: none;
    }

    .dropdown-menu.login_dropdown .modal .modal-header{
        display: block;
        text-align: center;
        border-bottom: 1px solid #eee !important;
    }
    .dropdown-menu.login_dropdown .modal .modal-header button{
        position: absolute;
        left: 12px;
        top: 25px;
        opacity: .3;
    }

}

/*work on 768px or higher*/
@media screen and (min-width: 768px) {
    .dropdown-menu.login_dropdown
    {
        position: absolute;
    }
    .dropdown-menu.login_dropdown .modal
    {
        position: fixed !important;
        top: 0%;
        right: 1% !important;
    }

    .dropdown-menu.login_dropdown .modal .modal-dialog
    {
        position: absolute !important;
        right: 1% !important;
        min-width:340px;
    }

}

@media all and (max-width: 1920px) {
    footer .container-fluid
    {
        max-width: 100%;
        margin: 0;
    }
    footer .footer_wrapper
    {
        padding: 0 60px 60px;
    }
}

@media all and (max-width: 1100px) {
    .navbar .nav-item .nav-link{
        padding: 5px 12px;
    }
}

@media all and (max-width: 991px) {

    .navbar-collapse {
        position: absolute;
        top: 0;
        right: -250px;
        padding-left: 0px;
        padding-right: 0px;
        padding-bottom: 15px;
        width: 250px;
        transition: all 0.3s ease;
        display: block;
        background-color: #000000;
        border-color: #000000;
        z-index: 999;
        padding-top: 50px;
        height: 100vh;
    }
    .navbar-collapse.collapsing {
        height: auto !important;
        transition: all 0.3s ease;
        display: block;
    }
    .navbar-collapse.show {
        width: 250px;
        right: 0;

    }
    .navbar-collapse.show .navbar-nav .nav-link
    {
        color: #fff;
    }
    .navbar-toggler .hamburger-toggle .active span
    {
        background: #fff;
        z-index: 99;
    }
    .navbar-toggler
    {
        position: absolute;
        right: 0;
        top: 5px;
    }

    .icon_menu .navbar-nav{
        display: -webkit-inline-box;
        margin-right: 40px;
    }
    .icon_menu .navbar-nav li.nav-item
    {
        float: left;
    }
    .action_button
    {
        display: block;
        color: #252525;
        background-color: #ffffff;
    }
    .action_btn_div_mobile
    {
        display: block !important;
        text-align: center;

    }
    .action_btn_div_mobile a
    {
        text-align: center;

    }

    .btn-secondary:not(:disabled):not(.disabled):active,.btn-secondary:not(:disabled):not(.disabled):focus
    {
        border:none !important;
        color: #0000FF !important;
        box-shadow:none !important;
    }

    .navbar-collapse.show .nav-item
    {
        width: 100%;
        position: relative;
        border-top: 1px solid hsla(0,0%,100%,.03);
    }
    .navbar-collapse.show .nav-item:last-child
    {
        border-bottom: 1px solid hsla(0,0%,100%,.03);
    }
    .navbar-collapse.show .nav-item.dropdown .nav-link.dropdown-toggle::after
    {
        position: absolute;
        right: 15px;
        content: "+";
        border: none;
        transition: all 0.3s ease;
        font-size: 20px;
    }
    .navbar-collapse.show .nav-item.dropdown .nav-link.dropdown-toggle.show::after
    {
        content: "-";
        font-size: 20px;
    }
    .navbar-collapse.show .nav-item.dropdown .nav-link.dropdown-toggle.show
    {
        color: #0000FF;
    }

    .navbar-collapse.show .nav-item.dropdown .dropdown-menu
    {
        background: hsla(0,0%,100%,.025);
        padding-top: 0px;
        transition: all 0.8s ease;
    }
    .navbar-collapse.show .nav-item.dropdown .dropdown-menu li
    {
        width: 100%;
        position: relative;
        border-top: 1px solid hsla(0,0%,100%,.03);
    }
    .navbar-collapse.show .nav-item.dropdown .dropdown-menu li:last-child
    {
        border-bottom: 1px solid hsla(0,0%,100%,.03);
    }
    .navbar-collapse.show .nav-item.dropdown .dropdown-menu li a
    {
        padding-left: 35px;
        display: block;
        padding: 11px 5px 10px 35px;
        margin-right: 50px;
        text-decoration: none;
        line-height: 19px;
        font: revert;
        vertical-align: baseline;
        font-size: 1rem;
    }
    .navbar-collapse.show .navbar-nav .nav-link
    {
        display: block;
        padding: 11px 5px 10px 20px;
        margin-right: 50px;
        text-decoration: none;
        line-height: 19px;
        font-size: 1rem;
        font-weight: 600;
        letter-spacing: 0rem;
    }
    .footer_description_div{
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 20px;
        padding-bottom: 25px;
    }
}
@media all and (min-width: 768px) and (max-width: 991px)
{
    .navbar .nav-item .nav-link
    {
        line-height: 50px;
    }

    .dropdown-menu.login_dropdown
    {
        position: absolute;
    }
    footer .about_des{
        font-size: 1rem;
        line-height: 1.4rem;
    }
    footer h5
    {
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 600;
        margin-top: 30px;
    }
    footer .menu_div ul li a {
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 400;
    }
    footer .copyright_div
    {
        padding: 0;
        margin: 0;
    }
    footer .copyright_div .copyright
    {
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 400;
        padding: 24px 0;
    }

}
@media all and (min-width: 576px) and (max-width: 767px)
{
    .navbar .nav-item .nav-link
    {
        line-height: 50px;
    }


    .dropdown-menu.login_dropdown
    {
        position: absolute;
    }


    .dropdown .dropdown-menu.login_dropdown {
        -webkit-transition: all 0.32s;
        -moz-transition: all 0.32s;
        -ms-transition: all 0.32s;
        -o-transition: all 0.32s;
        transition: all 0.32s;

        display: block;
        overflow: hidden;
        opacity: 0;
        transform: translateX(100%) translateY(0);
        transform-origin: left;
        top: 0;
        height: 100vh;
    }

    .dropdown-menu.login_dropdown.show {
        opacity: 1;
        transform: translateX(-18.5%) scaleY(1);
        z-index: 99995;
        width: 400px;
        padding: 0 20px;
    }

    .dropdown-menu.login_dropdown .modal-content
    {
        border: none;
    }
    footer
    {
        padding: 0;
        margin: 0;
    }
    footer .footer_wrapper{
        padding: 50px 0 50px 50px;
    }
    footer .about_des{
        margin-bottom: 0;
        line-height: 1.4rem;
        font-weight: 400;
        font-size: 1rem;
        max-width: 422px;
    }
    footer h5
    {
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 600;
        margin-top: 20px;
        margin-bottom: 30px;
    }
    footer .menu_div ul li a {
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 600;
    }
    footer .copyright_div
    {
        padding: 0;
        margin: 0;
        min-height: auto;
    }
    footer .copyright_div .copyright{
        float: none;
        text-align: left;
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 400;
        padding: 20px 0 30px 50px;
        margin-bottom: 0;
    }
}
@media all and (min-width: 425px) and (max-width: 576px)
{
    .dropdown-menu.login_dropdown
    {
        position: absolute;
    }


    .dropdown .dropdown-menu.login_dropdown {
        -webkit-transition: all 0.32s;
        -moz-transition: all 0.32s;
        -ms-transition: all 0.32s;
        -o-transition: all 0.32s;
        transition: all 0.32s;

        display: block;
        overflow: hidden;
        opacity: 0;
        transform: translateX(100%) translateY(0);
        transform-origin: left;
        top: 0;
        height: 100vh;
    }

    .dropdown-menu.login_dropdown.show {
        opacity: 1;
        transform: translateX(-4%) scaleY(1);
        z-index: 99995;
    }

    .dropdown-menu.login_dropdown .modal-content
    {
        border: none;
    }
}
@media all and (min-width: 320px) and (max-width: 425px)
{
    .dropdown-menu.login_dropdown
    {
        position: absolute;
    }


    .dropdown .dropdown-menu.login_dropdown {
        -webkit-transition: all 0.32s;
        -moz-transition: all 0.32s;
        -ms-transition: all 0.32s;
        -o-transition: all 0.32s;
        transition: all 0.32s;

        display: block;
        overflow: hidden;
        opacity: 0;
        transform: translateX(100%) translateY(0);
        transform-origin: left;
        top: 0;
        height: 100vh;
    }

    .dropdown-menu.login_dropdown.show {
        opacity: 1;
        transform: translateX(0%) scaleY(1);
        z-index: 99995;
    }

    .dropdown-menu.login_dropdown .modal-content
    {
        border: none;
    }
    .dropdown-menu.login_dropdown
    {
        width: 420px;
        position: fixed;
        max-width: 100%;
        left: 0;
    }
}
@media only screen and (max-width: 575px)
{
    .language-dropdown img {
        height: 18px;
        width: 23px;
    }
    #dropdownMenuButton {
        margin: 18px;
    }
    .navbar .nav-item .nav-link
    {
        line-height: 50px;
    }


    footer
    {
        padding: 0;
        margin: 0;
    }
    footer .footer_wrapper
    {
        padding:40px 20px 0;
    }
    footer .about_des{
        margin-bottom: 0;
        line-height: 20px;
        font-weight: 400;
        font-size: 1rem;
        max-width: 100%;
    }
    footer h5{
        text-transform: uppercase;
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 600;
        margin-bottom: 30px;
        margin-top: 20px;

    }
    footer .menu_div ul li a
    {
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 400;
        margin-bottom: 4px;
    }
    footer .copyright_div
    {
        padding: 0;
        min-height: auto;
    }
    footer .copyright_div .copyright{
        float: none;
        text-align: center;
        font-size: 1rem;
        line-height: 1.4rem;
        font-weight: 400;
        padding: 18px 0 22px;
        margin-bottom: 0;
    }
}


/*Responsive End*/
