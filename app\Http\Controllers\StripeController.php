<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Services\StripeService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Stripe;

class StripeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|\Illuminate\Http\Response|View
     */
    public function index()
    {
        $methods = Auth::user()->payment_methods;

        return view('user.stripe-card.list', compact('methods'));
    }

    /**
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('user.stripe-card.create');
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $paymentMethods = [];
        $fingerprint = Stripe::paymentMethods()->find($request->paymentMethodId)['card']['fingerprint'];
        if (auth()->user()->stripe_id) {
            $paymentMethods = Stripe::paymentMethods()->all([
                'type' => 'card',
                'customer' => auth()->user()->stripe_id,
            ]);
            $fingerprints = array_map(function ($val) {
                return Stripe::paymentMethods()->find($val['id'])['card']['fingerprint'];
            }, $paymentMethods['data']);
        }
        if (! in_array($fingerprint, $fingerprints)) {

            try {
                (new StripeService)->attachPaymentMethodToCustomar($request->paymentMethodId);
            } catch (\Exception $exception) {
                $msg = $exception->getMessage() == 'Your card was declined.' ? $exception->getMessage() : 'You have submitted wrong expire date or wrong CVC';

                return response()->json(['msg' => $msg, 'status' => false], 400);
            }

            $methods = Auth::user()->payment_methods;

            return response()->json([
                'msg' => 'Payment method added successfully.',
                'status' => true,
                'method_number' => count($methods) > 0 ? $methods[0]['last_four'] : 0,

            ], 200);
        } else {
            return response()->json(['msg' => 'Payment method already exists in your account.', 'status' => false], 409);
        }
    }

    public function updateSubscription(Request $request)
    {
        (new StripeService)->updateSubscription(auth()->user()->stripe_id, $request->subscription_id, $request->payment_method);

        return redirect()->back();
    }

    public function getSubscriptionPaymentMethod(Request $request)
    {
        $subscription = Subscription::where('subscription_id', $request->subscription_id)->first();
        $default_payment_method = null;
        if (strpos($subscription->subscription_id, 'sched')) {
            $schedule = (new StripeService)->getSubscriptionSchedule($subscription);
            if ($schedule->status != 'canceled') {
                $default_payment_method = $schedule->default_settings->default_payment_method;
            }
        } else {
            $subscription = (new StripeService)->getSubscription($subscription);
            if ($subscription['status'] != 'canceled') {
                $default_payment_method = $subscription['default_payment_method'];
            }
        }

        return response()->json(['payment_method' => $default_payment_method], 200);
    }

    public function destroy($paymentMethodID)
    {
        Stripe::paymentMethods()->detach($paymentMethodID);

        return redirect()->back()->with('success', 'Payment method deleted successfully.');
    }
}
