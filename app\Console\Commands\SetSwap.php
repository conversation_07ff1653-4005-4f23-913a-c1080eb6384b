<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class SetSwap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'swap:set';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set Swap Space to 2GB';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // return 0;
        $this->info('Setting swap space to 2GB...');

        $commands = [
            'sudo swapoff -a',
            'sudo fallocate -l 2G /swapfile',
            'sudo chmod 600 /swapfile',
            'sudo mkswap /swapfile',
            'sudo swapon /swapfile',
            'echo \'/swapfile none swap sw 0 0\' | sudo tee -a /etc/fstab',
        ];

        foreach ($commands as $command) {
            $process = new Process(explode(' ', $command));
            $process->run();

            if (! $process->isSuccessful()) {
                $this->error("Error executing command: $command");

                return;
            }
        }

        $this->info('Swap space set to 2GB successfully.');
    }
}
