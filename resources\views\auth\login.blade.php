@extends('frontend.layouts.app_login')
@section('page-title')
    Login
@endsection
@section('content')
    <!-- Enhanced Login Section with Modern Design -->
    <div class="fancy-login-wrapper">
        <!-- Animated Background -->
        <div class="animated-bg">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
            </div>
        </div>

        <div class="login-section user-account d-flex flex-wrap">
            <div class="left-section enhanced-left">
                <a href="@if(session('locale') != 'kr') {{ route('landingEn') }} @else {{ route('landingKr')}} @endif" class="logo-link">
                    <img src="{{ asset('images/new-logo-white.svg') }}" alt="codibu logo" class="logo-img">
                </a>
                <div class="d-flex flex-column h-100 justify-content-center">
                    <div class="welcome-content">
                        <h1 class="welcome-title">
                            <span class="gradient-text">{{ __('login.One-Stop') }}</span>
                            <br><span class="gradient-text">{{ __('login.Website') }}</span>
                            <br><span class="gradient-text">{{ __('login.Solution') }}</span>
                        </h1>
                        <p class="welcome-subtitle">Experience the future of web development with our cutting-edge platform</p>
                    </div>
                </div>
            </div>
            <div class="align-items-sm-center d-flex right-section enhanced-right">

            <div class="login-content">

                @if (Session::has('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <p>{{Session::get('success')}}</p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @elseif(Session::has('warning'))
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <p>{{Session::get('warning')}}</p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @elseif(Session::has('danger'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <p>{{Session::get('danger')}}</p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @elseif(Session::has('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <p>{{Session::get('info')}}</p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @elseif (Session::has('message'))
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <p>{{Session::get('message')}}</p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif


                <a href="https://codibu.com" class="logo d-block d-sm-none">
                    <img href="{{ url('') }}" src="{{ asset('images/new-logo.svg') }}" alt="codibu logo">
                </a>
                <h2> {{ __('login.login-to') }}</h2>

                <div class="divider" data-text="{{ __('login.data-text1') }}"></div>
                <div class="social-login d-sm-flex d-block">
                    @include('frontend.component.social_login')
                </div>
                <div class="divider" data-text=" {{ __('login.data-text2') }}"></div>

                <form method="POST" action="{{ route('login') }}">
                    @csrf
                    <div class="group-input">
                        <!-- <label for="email" class="form-label">Email address*</label> -->
                        <div class="border-gradient @error('email') is-invalid-error @enderror">
                            <input type="email" class="control-form" name="email" id="email"
                                   placeholder="{{ __('<EMAIL>') }}" value="{{ old('email') }}" required>
                            <img class="position-icon cursor-pointer" src="{{ asset('images/icon/check-gradient.svg') }}"
                                 alt="codibu">
                        </div>
                        @error('email')
                        <div class="invalid-feedback-error">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>
                    <div class="group-input">
                        <!-- <label for="password" class="form-label">Password*</label> -->
                        <div class="border-gradient position-relative @error('password') is-invalid-error @enderror">
                            <input type="password" name="password" required class="control-form" id="password"
                                   placeholder="********">
                            <img class="eye-off pass-eye position-icon active cursor-pointer"
                                 src="{{ asset('images/icon/eye-off.svg') }}" alt="codibu">
                            <img class="eye-on pass-eye position-icon cursor-pointer"
                                 src="{{ asset('images/icon/eye-on.svg') }}" alt="codibu">


                            @error('password')
                            <div class="invalid-feedback-error">
                                {{ $message }}
                            </div>
                            @enderror

                        </div>


                    </div>

                    <div class="group-input">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember"
                                {{ old('remember') ? 'checked' : '' }}>{{ __('login.remember-me') }}
                            <label class="form-check-label" for="remember">
                                {{ __('Remember Me') }}
                            </label>
                            <a href="{{ route('password.request') }}" class="float-end">{{ __('login.forgot') }}</a>
                        </div>
                    </div>

                    <button class="hover-animation login-button" type="submit" id="loginBtn"> {{ __('login.Login') }}</button>


                    <div class="divider" data-text=""></div>

                    <div class="group-input" style="text-align: center">
                        <a href="@if(session('locale') != 'kr') {{ url('/register') }} @else {{ url('/register/kr')}} @endif" id="signuplink">{{ __('login.signUp') }}</a>
                    </div>

                </form>
                <div style="text-align: center">
                    <label for="cars" style="margin-right:10px;">Choose Language:</label>
                    <span class="outerFilterBox">
                    <select name="" id="filter">
                        <option value="en" @if(session('locale') == 'en') selected @endif>English</option>
                        <option value="kr" @if(session('locale') == 'kr') selected @endif>Korean</option>
                    </select>
                </span>
                </div>
            </div>
        </div>
    </div>
@endsection
