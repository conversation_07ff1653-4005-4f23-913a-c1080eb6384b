<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DebugOn extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:on';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will turn on debug mode.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $_ENV['APP_DEBUG'] = true;
        $this->call('config:cache');
        echo "Debug mode is turned on.\n";
        echo env('APP_DEBUG');
    }
}
