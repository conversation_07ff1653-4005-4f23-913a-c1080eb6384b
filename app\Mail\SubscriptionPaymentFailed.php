<?php

namespace App\Mail;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SubscriptionPaymentFailed extends Mailable
{
    use Queueable, SerializesModels;

    public $subscription;

    public $last_Date;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Subscription $subscription, $last_Date)
    {
        $this->subscription = $subscription;
        $this->last_Date = $last_Date;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'CODIBU')
            ->subject('Your Subscription Payment Failed for '.$this->subscription->serviceable_name)
            ->view('emails.payment_failed');
    }
}
