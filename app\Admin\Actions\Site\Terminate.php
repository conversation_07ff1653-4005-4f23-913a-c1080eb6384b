<?php

namespace App\Admin\Actions\Site;

use App\Models\Site;
use App\Repository\SubscriptionRepository;
use App\Services\LightsailService;
use App\Services\PaypalService;
use App\Services\StripeService;
use Encore\Admin\Actions\Response;
use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;

class Terminate extends RowAction
{
    public $name = 'Terminate';

    public function handle(Model $model): Response
    {
        $id = $model->id;
        $site = Site::find($id);

        $site->update(['status' => 'terminated']);

        try {
            (new LightsailService($site->user->aws_account_id, $site->aws_region))->stopInstance($site);
            if ($site->subscription) {
                if ($site->subscription->platform == 'Stripe') {
                    (new StripeService)->cancelSubscription(auth()->user()->stripe_id, $site->subscription->subscription_id);
                } else {
                    (new PaypalService)->cancelSubscription($site->subscription->subscription_id, 'change');
                }
                $site->subscription->update(['status' => 'Inactive']);
            }
        } catch (\Throwable $th) {

        }
        (new SubscriptionRepository)->cancelSubscription($site->subscription->id);

        return $this->response()->success('Success! This site is Terminated')->refresh();
    }

    public function form()
    {
        $this->text('answer', '5 X 6 = ?')->rules('required|regex:(30)', [
            'required' => 'Please enter the answer.',
            'regex' => 'Incorrect answer.',
        ]);
    }
}
