<?php

namespace App\Http\Controllers;

use App\Models\EmailSubscribe;
use Illuminate\Http\Request;

class EmailSubscribeController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        $id = Auth()->user()->id;
        $task_email = false;
        $ticket_email = false;
        $ads_email = false;
        $unsubscribe_email = false;
        if ($request['unsubscribe_email'] != 'true') {
            if ($request['task_email'] == 'true') {
                $task_email = true;
            }
            if ($request['ticket_email'] == 'true') {
                $ticket_email = true;
            }
            if ($request['ads_email'] == 'true') {
                $ads_email = true;
            }
        }

        $result = [
            'user_id' => $id,
            'task' => $task_email,
            'ticket' => $ticket_email,
            'ads' => $ads_email,
        ];

        EmailSubscribe::create($result);

        //  return redirect(url()->previous())->with('successMsg', 'Thank you for subscribing to our newsletter!');
        return response()->json(['status' => 'success']);
    }
}
