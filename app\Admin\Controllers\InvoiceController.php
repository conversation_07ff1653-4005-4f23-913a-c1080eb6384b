<?php

namespace App\Admin\Controllers;

use App\Mail\InvoiceMail;
use App\Models\ProfessionalServicePlan;
use App\Models\ServiceCategories;
use App\Services\PaypalService;
use App\Services\StripeService;
use App\User;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Mail;

class InvoiceController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Custom Invoice';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ProfessionalServicePlan);

        $grid->model()->whereNotNull('custom_invoice');
        $grid->column('id', __('Id'));
        $grid->column('name', __('Name'));
        $grid->column('category.name', __('Category'));
        $grid->column('user.email', __('Email'));
        $grid->column('price', __('Price'));
        $grid->column('duration', __('Duration'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(ProfessionalServicePlan::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('name', __('Name'));
        $show->field('category.name', __('Category'));
        $show->field('user.email', __('Email'));
        $show->field('price', __('Price'));
        $show->field('duration', __('Duration'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ProfessionalServicePlan);
        $duration = [
            'one-time' => 'One Time',
            'week' => 'Week',
            'month' => 'Month',
            'year' => 'Year',
        ];
        $categories = ServiceCategories::all()->pluck('name', 'id');
        $user = User::all()->pluck('name', 'id');
        $form->text('name', __('Product Name'))->required();
        $form->select('user_id', __('User'))->options($user)->required();
        $form->select('service_category_id', __('Category'))->options($categories)->required();

        if (substr(trim(request()->path(), '/'), -5) == '/edit') {
            $form->select('duration', __('Duration'))->options($duration)->disable();
            $form->hidden('duration');
            $form->currency('price', __('Price'))->disable();
            $form->hidden('price');
        } else {
            $form->select('duration', __('Duration'))->options($duration)->required();
            $form->currency('price', __('Price'))->required();
        }
        $form->hidden('paypal_plan_id');
        $form->hidden('stripe_plan_id');
        $form->hidden('custom_invoice')->default(Crypt::encrypt(rand(1, 100000)));

        $form->saved(function (Form $form) {
            if ($form->duration != 'one-time') {
                if ($form->model()->toArray()['paypal_plan_id'] == null) {
                    (new PaypalService)->createProfessionServicePlan($form->model()->toArray()['id']);
                }
                if ($form->model()->toArray()['stripe_plan_id'] == null) {
                    (new StripeService)->createProfessionServicePlan($form->model()->toArray()['id']);
                }
            }
            Mail::to($form->model()->user)->queue(new InvoiceMail($form->model()->toArray()['id']));
        });

        return $form;
    }
}
