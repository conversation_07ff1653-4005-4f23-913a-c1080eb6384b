<?php

namespace App\Console\Commands;

use App\User;
use Aws\Organizations\OrganizationsClient;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class SetAwsOrgAccountIdInUserModel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'toprankon:addiamkeypair';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $client = new OrganizationsClient([
            'profile' => 'default',
            'region' => 'us-east-1',
            'version' => 'latest',
        ]);
        $result = $client->listAccounts();
        $nextToken = null;
        while (true) {
            $result = $client->listAccounts([
                'MaxResults' => 20,
                'NextToken' => $nextToken,
            ]);
            $accounts[] = $result['Accounts'];
            if ($result['NextToken']) {
                $nextToken = $result['NextToken'];
            } else {
                break;
            }
        }
        foreach (User::where('aws_account_id', null)->where('email_verified_at', '!=', null)->get() as $user) {
            foreach (Arr::collapse($accounts) as $account) {
                $explode_data = explode('@', $account['Email']);
                if ($explode_data[0] == $user->id) {
                    Log::info('user id : '.$explode_data[0]);
                    dispatch(new \App\Jobs\SetAwsOrgAccountIdInUserModelJob($account['Id'], $user->id));
                }
            }
        }
    }
}
