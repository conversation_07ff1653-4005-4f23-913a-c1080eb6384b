<?php

namespace App\Admin\Actions\Site;

use App\Models\Site;
use Encore\Admin\Actions\BatchAction;
use Encore\Admin\Actions\Response;
use Illuminate\Database\Eloquent\Collection;

class BatchTerminate extends BatchAction
{
    public $name = 'Batch Terminate';

    public function handle(Collection $collection): Response
    {
        foreach ($collection as $model) {
            $id = $model->id;
            $site = Site::find($id);
            $site->delete();
        }

        return $this->response()->success('Success! All site are Terminated')->refresh();
    }

    public function form()
    {
        $this->text('answer', '5 X 6 = ?')->rules('required|regex:(30)', [
            'required' => 'Please enter the answer.',
            'regex' => 'Incorrect answer.',
        ]);
    }
}
