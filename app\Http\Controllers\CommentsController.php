<?php

namespace App\Http\Controllers;

use App\Mail\CommentTicket;
use App\Models\Comment;
use App\Models\Ticket;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class CommentsController extends Controller
{
    /**
     * Persist comment and mail user
     *
     * @param  AppMailer  $mailer
     * @return Response
     */
    public function postComment(Request $request)
    {
        $this->validate($request, [
            'comment' => 'required',
        ]);

        $comment = Comment::create([
            'ticket_id' => $request->input('ticket_id'),
            'user_id' => Auth::user()->id,
            'comment' => $request->input('comment'),
        ]);

        // send mail if the user commenting is not the ticket owner
        Mail::to('<EMAIL>')->send(new CommentTicket(Auth::user()->name, Auth::user()->email, $comment->ticket, $comment));

        return redirect()->back()->with('scc_msg', 'Your reply has been submitted.');
    }
}
