<?php

namespace App\Http\Controllers;

use App\Http\Requests\ChangeDomain;
use App\Http\Requests\PurchaseDomain;
use App\Models\DomainRenewPlan;
use App\Models\PaymentPlatform;
use App\Models\Site;
use App\Resolver\PaymentPlatformResolver;
use App\Services\DomainChangeService;
use Jenssegers\Agent\Agent;

class ChangeDomainController extends Controller
{
    /**
     * @var ;
     */
    protected $paymentPlatformResolver;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(PaymentPlatformResolver $paymentPlatformResolver)
    {
        $this->paymentPlatformResolver = $paymentPlatformResolver;
    }

    public function changeDomain(ChangeDomain $request, $id)
    {
        $request->validated();
        session()->put('domain', request('domain'));
        (new DomainChangeService)->domainChange(Site::findOrFail($id));

        return response()->json(['domain' => request('domain')], 200);
    }

    public function getPurchaseDomain($id)
    {
        session()->forget(codibu_session());
        $data = [
            'site' => Site::findOrFail($id),
            'paymentPlatforms' => PaymentPlatform::where('name', '!=', 'Amazon')->get(),
            'payment_methods' => auth()->user()->payment_methods,
            'is_windows' => (new Agent)->is('Windows'),
        ];

        return view('sites.changeDomain', $data);
    }

    public function postPurchaseDomain(PurchaseDomain $request, $id)
    {
        $request->validated();

        $domain_plan = DomainRenewPlan::where('price', session('domain_price'))->first();
        $arr = array_merge($request->all(), [
            'domain' => $request->domain,
            'paypal_plan_id' => $domain_plan->paypal_plan_id,
            'stripe_plan_id' => $domain_plan->stripe_plan_id,
            'plan' => $domain_plan->id,
            'site_id' => $id,
            'domain_price' => session('domain_price'),
        ]);
        session()->put($arr);
        // payment start
        if ($request->payment_platform) {
            $paymentPlatform = $request->payment_platform != 'Amazon'
                ? $this->paymentPlatformResolver->resolveServices(session('payment_platform'))
                : '';
            $paypal_url = '';
            if ($request->payment_platform == 'PayPal') {
                $paypal_url = $paymentPlatform->handleDomainSubscription();
            } elseif ($request->payment_platform == 'Stripe') {
                $paymentPlatform->handleDomainSubscription();
            } else {
                session()->put('plan_id', $request->input('plan'));
            }
        } else {
            return ['errors' => ['payment' => 'Payment method not provided.']];
        }
        //payment end

        $data = [
            'status' => true,
            'paypal_url' => $paypal_url,
            'payment_platform' => $request->payment_platform,
        ];

        return response()->json($data, 200);
    }
}
