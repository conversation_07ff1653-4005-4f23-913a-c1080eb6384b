<?php

namespace App\Mail;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserWithProfessionalService extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The site instance.
     *
     * @var Site
     */
    public $subscription;

    public $is_new = false;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Subscription $subscription, $is_new = false)
    {
        $this->subscription = $subscription;
        $this->is_new = $is_new;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'CODIBU')
            ->subject('Your '.$this->subscription->serviceable->name.' package is being Purchased!')
            ->view('emails.user-with-professional-service');
    }
}
