<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Newsletter;
use App\Rules\RecaptchaCheck;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NewsletterController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'recaptcha_token' => ['bail', 'required', new RecaptchaCheck],
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return redirect(url()->previous().'#newsletter')->withErrors($validator);
        }

        Newsletter::create($request->all());

        return redirect(url()->previous().'#newsletter')->with('successMsg', 'Thank you for subscribing to our newsletter!');
    }
}
