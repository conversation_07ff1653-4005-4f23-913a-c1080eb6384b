<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class DnsValidation extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        if ($request->dns_name == 'SRV') {
            return [
                'priority' => 'bail|required|integer|between:0,65535',
                'weight' => 'bail|required|integer|between:0,65535',
                'name' => 'bail|required',
                'value' => 'bail|required',
                'port' => 'bail|required|integer|between:0,65535',
            ];
        } elseif ($request->dns_name == 'MX') {
            return [
                'priority' => 'bail|required|integer|between:0,65535',
                'name' => 'bail|required',
                'value' => 'bail|required',
            ];
        } elseif ($request->dns_name == 'A') {
            return [
                'name' => 'bail|required',
                'value' => 'bail|required|ipv4',
            ];
        } elseif ($request->dns_name == 'AAAA') {
            return [
                'name' => 'bail|required',
                'value' => 'bail|required|ipv6',
            ];
        } else {
            return [
                'name' => 'bail|required',
                'value' => 'bail|required',
            ];
        }
    }
}
