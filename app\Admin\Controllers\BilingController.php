<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Site\Payment\PartialRefund;
use App\Admin\Actions\Site\Payment\Refund;
use App\Models\Subscription;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class BilingController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Subscription';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Subscription);

        $grid->column('id', __('Id'));
        $grid->column('user_id', __('User id'));
        $grid->column('subscribable_type', __('Subscribable type'))->display(function ($type) {

            return explode('\\', $type)[2] == 'Plan' ? 'HostingPlan' : explode('\\', $type)[2];

        });
        $grid->column('subscribable_name', __('Subscribable name'));
        $grid->column('serviceable_type', __('Serviceable type'))->display(function ($type) {

            return explode('\\', $type)[2];

        });
        $grid->column('serviceable_name', __('Serviceable name'));
        $grid->column('platform', __('Platform'));
        $grid->column('total_price', __('Total price'));
        $grid->column('next_billing_date', __('Next billing date'));
        $grid->column('status', __('Status'));
        $grid->column('created_at', __('Created at'));
        $grid->column('canceled_at', __('Canceled at'));
        // Delete Terminate
        $grid->actions(function ($actions) {
            $actions->disableEdit();
            $actions->disableDelete();
        });

        $grid->tools(function ($tools) {
            $tools->batch(function ($batch) {
                $batch->disableDelete();
            });
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Subscription::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('user_id', __('User id'));
        $show->field('subscribable_name', __('Subscribable name'));
        $show->field('serviceable_type', __('Serviceable type'))->as(function ($type) {
            return explode('\\', $type)[2];
        });
        $show->field('serviceable_name', __('Serviceable name'));
        $show->field('platform', __('Platform'));
        $show->field('total_price', __('Total price'));
        $show->field('next_billing_date', __('Next billing date'));
        $show->field('status', __('Status'));
        $show->field('created_at', __('Created at'));
        $show->field('canceled_at', __('Canceled at'));
        $show->panel()
            ->tools(function ($tools) {
                $tools->disableEdit();
                $tools->disableDelete();
            });
        $show->paymentHistories('Payment History', function ($payment) {
            $payment->id();
            $payment->payment_platform();
            $payment->payment_amount('Paid Amount ($)');
            $payment->refund_amount('Refund Amount ($)');
            $payment->created_at('Paid On')->display(function ($createdAt) {
                return Carbon::parse($createdAt)->format('m/d/Y h:i a');
            });
            $payment->disablePagination();

            $payment->disableCreateButton();

            $payment->disableFilter();

            $payment->disableRowSelector();

            $payment->disableColumnSelector();

            $payment->disableTools();

            $payment->disableExport();

            $payment->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->disableEdit();
                $actions->disableDelete();
                $actions->add(new Refund);
                $actions->add(new PartialRefund);
            });

        });

        return $show;
    }
}
