<?php

/**
 * return formatted date
 */
use Carbon\Carbon;

if (! function_exists('remaining_downloads')) {
    function remaining_downloads(): array
    {
        $site_list = [];
        $user = auth()->user();
        $user_sites = $user->sites->where('status', 'completed');
        if (count($user_sites) > 0) {
            $downloads = $user->downloads;
            foreach ($user_sites as $key => $site) {
                $download_limit = optional(optional($site->subscription)->subscribable)->download_limit;
                $site_list[$key]['remaining_limit'] = $download_limit > $downloads ? $download_limit - $downloads : 0;
                $site_list[$key]['reset_date'] = optional($site->subscription)->latestPaymentHistory ? Carbon::parse(optional($site->subscription)->latestPaymentHistory->start_date)->format('d F Y') : '';
                $site_list[$key]['next_reset_date'] = optional($site->subscription)->latestPaymentHistory ? Carbon::parse(optional($site->subscription)->latestPaymentHistory->end_date)->format('d F Y') : '';
                $downloads = $site_list[$key]['remaining_limit'] == 0 ? $downloads - $download_limit : 0;
            }
        }

        return $site_list;
    }
}
